from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

db = SQLAlchemy()

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(20), nullable=False)  # 'admin' or 'cashier'
    
    def __init__(self, username, password, role):
        self.username = username
        self.password_hash = generate_password_hash(password)
        self.role = role
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    category = db.Column(db.String(50), nullable=False)
    price = db.Column(db.Float, nullable=False)
    
    order_items = db.relationship('OrderItem', backref='product', lazy=True)

class Table(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    status = db.Column(db.String(20), nullable=False, default='available')  # 'available', 'occupied'
    
    orders = db.relationship('Order', backref='table', lazy=True)

class Order(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    table_id = db.Column(db.Integer, db.ForeignKey('table.id'), nullable=False)
    date = db.Column(db.DateTime, nullable=False, default=datetime.now)
    total = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(20), nullable=False, default='pending')  # 'pending', 'completed', 'cancelled'
    
    items = db.relationship('OrderItem', backref='order', lazy=True, cascade="all, delete-orphan")

class OrderItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)

def init_db():
    db.create_all()

    # Create admin user if it doesn't exist
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(username='admin', password='admin123', role='admin')
        db.session.add(admin)

    # Create cashier user if it doesn't exist
    cashier = User.query.filter_by(username='cashier').first()
    if not cashier:
        cashier = User(username='cashier', password='cashier123', role='cashier')
        db.session.add(cashier)

    # Add some sample data for testing if products don't exist
    if Product.query.count() == 0:
        # Sample products
        products = [
            Product(name='Burger', category='Food', price=9.99),
            Product(name='Pizza', category='Food', price=12.99),
            Product(name='Salad', category='Food', price=7.99),
            Product(name='Fries', category='Food', price=4.99),
            Product(name='Sandwich', category='Food', price=8.99),
            Product(name='Soda', category='Drink', price=2.99),
            Product(name='Coffee', category='Drink', price=3.99),
            Product(name='Tea', category='Drink', price=2.49),
            Product(name='Juice', category='Drink', price=3.49),
            Product(name='Ice Cream', category='Dessert', price=4.99),
            Product(name='Cake', category='Dessert', price=5.99),
            Product(name='Cookies', category='Dessert', price=3.99)
        ]
        for product in products:
            db.session.add(product)

    # Add sample tables if they don't exist
    if Table.query.count() == 0:
        tables = [
            Table(name='Table 1', status='available'),
            Table(name='Table 2', status='available'),
            Table(name='Table 3', status='available'),
            Table(name='Table 4', status='available'),
            Table(name='Table 5', status='available'),
            Table(name='Table 6', status='available')
        ]
        for table in tables:
            db.session.add(table)

    # Commit all changes
    db.session.commit()
        
