"""
Customer Management API for Cash Register Pro
Handles customer information, loyalty programs, and customer analytics
"""

from flask import Blueprint, request, jsonify
from db.models import db
from api.auth import token_required, admin_required
from datetime import datetime, timedelta
from sqlalchemy import func, text
import re

customers = Blueprint('customers', __name__)

# Since we don't have a Customer model, we'll simulate customer data
# In a real implementation, you would create a Customer model

@customers.route('/customers', methods=['GET'])
@token_required
def get_customers(current_user):
    """Get all customers with pagination"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')
        
        # Simulate customer data
        simulated_customers = []
        for i in range(1, 101):  # 100 customers
            customer = {
                'id': i,
                'name': f'Customer {i}',
                'email': f'customer{i}@example.com',
                'phone': f'******-{1000 + i:04d}',
                'total_orders': (i * 3) % 20 + 1,
                'total_spent': round((i * 47.5) % 500 + 50, 2),
                'last_visit': (datetime.now() - timedelta(days=(i % 30))).strftime('%Y-%m-%d'),
                'loyalty_points': (i * 15) % 200,
                'status': 'active' if i % 10 != 0 else 'inactive',
                'created_at': (datetime.now() - timedelta(days=i * 2)).strftime('%Y-%m-%d')
            }
            
            # Apply search filter
            if search and search.lower() not in customer['name'].lower() and search not in customer['email']:
                continue
                
            simulated_customers.append(customer)
        
        # Apply pagination
        start = (page - 1) * per_page
        end = start + per_page
        paginated_customers = simulated_customers[start:end]
        
        return jsonify({
            'customers': paginated_customers,
            'total': len(simulated_customers),
            'page': page,
            'per_page': per_page,
            'total_pages': (len(simulated_customers) + per_page - 1) // per_page
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@customers.route('/customers/<int:customer_id>', methods=['GET'])
@token_required
def get_customer(current_user, customer_id):
    """Get specific customer details"""
    try:
        # Simulate customer data
        if customer_id < 1 or customer_id > 100:
            return jsonify({'error': 'Customer not found'}), 404
        
        customer = {
            'id': customer_id,
            'name': f'Customer {customer_id}',
            'email': f'customer{customer_id}@example.com',
            'phone': f'******-{1000 + customer_id:04d}',
            'address': f'{customer_id * 10} Main Street, City, State 12345',
            'date_of_birth': f'199{customer_id % 10}-{(customer_id % 12) + 1:02d}-{(customer_id % 28) + 1:02d}',
            'total_orders': (customer_id * 3) % 20 + 1,
            'total_spent': round((customer_id * 47.5) % 500 + 50, 2),
            'average_order_value': round(((customer_id * 47.5) % 500 + 50) / ((customer_id * 3) % 20 + 1), 2),
            'last_visit': (datetime.now() - timedelta(days=(customer_id % 30))).strftime('%Y-%m-%d'),
            'loyalty_points': (customer_id * 15) % 200,
            'loyalty_tier': 'Gold' if (customer_id * 15) % 200 > 150 else 'Silver' if (customer_id * 15) % 200 > 75 else 'Bronze',
            'status': 'active' if customer_id % 10 != 0 else 'inactive',
            'created_at': (datetime.now() - timedelta(days=customer_id * 2)).strftime('%Y-%m-%d'),
            'notes': f'Preferred customer #{customer_id}. Likes special offers.',
            'preferences': {
                'communication': 'email',
                'newsletter': True,
                'promotions': customer_id % 3 == 0
            }
        }
        
        return jsonify(customer), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@customers.route('/customers', methods=['POST'])
@token_required
def create_customer(current_user):
    """Create a new customer"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['name', 'email']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        # Validate email format
        email = data['email']
        if not re.match(r'^[^\s@]+@[^\s@]+\.[^\s@]+$', email):
            return jsonify({'error': 'Invalid email format'}), 400
        
        # Simulate customer creation
        new_customer = {
            'id': 101,  # Next available ID
            'name': data['name'],
            'email': email,
            'phone': data.get('phone', ''),
            'address': data.get('address', ''),
            'date_of_birth': data.get('date_of_birth', ''),
            'total_orders': 0,
            'total_spent': 0.0,
            'average_order_value': 0.0,
            'last_visit': None,
            'loyalty_points': 0,
            'loyalty_tier': 'Bronze',
            'status': 'active',
            'created_at': datetime.now().strftime('%Y-%m-%d'),
            'notes': data.get('notes', ''),
            'preferences': {
                'communication': data.get('communication_preference', 'email'),
                'newsletter': data.get('newsletter_subscription', True),
                'promotions': data.get('promotion_emails', True)
            }
        }
        
        return jsonify({
            'message': 'Customer created successfully',
            'customer': new_customer
        }), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@customers.route('/customers/<int:customer_id>', methods=['PUT'])
@token_required
def update_customer(current_user, customer_id):
    """Update customer information"""
    try:
        data = request.get_json()
        
        if customer_id < 1 or customer_id > 100:
            return jsonify({'error': 'Customer not found'}), 404
        
        # Simulate customer update
        updated_customer = {
            'id': customer_id,
            'name': data.get('name', f'Customer {customer_id}'),
            'email': data.get('email', f'customer{customer_id}@example.com'),
            'phone': data.get('phone', f'******-{1000 + customer_id:04d}'),
            'address': data.get('address', f'{customer_id * 10} Main Street, City, State 12345'),
            'date_of_birth': data.get('date_of_birth', f'199{customer_id % 10}-{(customer_id % 12) + 1:02d}-{(customer_id % 28) + 1:02d}'),
            'notes': data.get('notes', f'Preferred customer #{customer_id}. Likes special offers.'),
            'status': data.get('status', 'active'),
            'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return jsonify({
            'message': 'Customer updated successfully',
            'customer': updated_customer
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@customers.route('/customers/<int:customer_id>/loyalty', methods=['GET'])
@token_required
def get_customer_loyalty(current_user, customer_id):
    """Get customer loyalty information"""
    try:
        if customer_id < 1 or customer_id > 100:
            return jsonify({'error': 'Customer not found'}), 404
        
        loyalty_points = (customer_id * 15) % 200
        
        # Determine tier based on points
        if loyalty_points >= 150:
            tier = 'Gold'
            tier_benefits = ['20% discount', 'Free delivery', 'Priority support', 'Birthday rewards']
            next_tier = None
            points_to_next = 0
        elif loyalty_points >= 75:
            tier = 'Silver'
            tier_benefits = ['10% discount', 'Free delivery on orders $50+', 'Birthday rewards']
            next_tier = 'Gold'
            points_to_next = 150 - loyalty_points
        else:
            tier = 'Bronze'
            tier_benefits = ['5% discount', 'Birthday rewards']
            next_tier = 'Silver'
            points_to_next = 75 - loyalty_points
        
        # Simulate transaction history
        transactions = []
        for i in range(min(10, (customer_id % 15) + 1)):
            transactions.append({
                'date': (datetime.now() - timedelta(days=i * 7)).strftime('%Y-%m-%d'),
                'type': 'earned' if i % 3 != 0 else 'redeemed',
                'points': (i + 1) * 5 if i % 3 != 0 else -(i + 1) * 10,
                'description': f'Order #{1000 + i}' if i % 3 != 0 else f'Discount applied to Order #{1000 + i}',
                'order_total': round((i + 1) * 25.50, 2)
            })
        
        loyalty_info = {
            'customer_id': customer_id,
            'current_points': loyalty_points,
            'tier': tier,
            'tier_benefits': tier_benefits,
            'next_tier': next_tier,
            'points_to_next_tier': points_to_next,
            'lifetime_points_earned': loyalty_points + 50,  # Simulate lifetime total
            'points_redeemed': 50,  # Simulate redeemed points
            'transactions': transactions,
            'available_rewards': [
                {'name': '$5 Off', 'points_required': 50, 'available': loyalty_points >= 50},
                {'name': '$10 Off', 'points_required': 100, 'available': loyalty_points >= 100},
                {'name': 'Free Appetizer', 'points_required': 75, 'available': loyalty_points >= 75},
                {'name': 'Free Dessert', 'points_required': 60, 'available': loyalty_points >= 60}
            ]
        }
        
        return jsonify(loyalty_info), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@customers.route('/customers/<int:customer_id>/loyalty/redeem', methods=['POST'])
@token_required
def redeem_loyalty_points(current_user, customer_id):
    """Redeem loyalty points for rewards"""
    try:
        data = request.get_json()
        reward_name = data.get('reward_name')
        points_to_redeem = data.get('points', 0)
        
        if not reward_name or points_to_redeem <= 0:
            return jsonify({'error': 'Invalid reward or points amount'}), 400
        
        if customer_id < 1 or customer_id > 100:
            return jsonify({'error': 'Customer not found'}), 404
        
        current_points = (customer_id * 15) % 200
        
        if points_to_redeem > current_points:
            return jsonify({'error': 'Insufficient loyalty points'}), 400
        
        # Simulate redemption
        new_points = current_points - points_to_redeem
        
        redemption = {
            'customer_id': customer_id,
            'reward_name': reward_name,
            'points_redeemed': points_to_redeem,
            'points_before': current_points,
            'points_after': new_points,
            'redemption_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'redemption_code': f'REWARD{customer_id}{int(datetime.now().timestamp())}'
        }
        
        return jsonify({
            'message': 'Loyalty points redeemed successfully',
            'redemption': redemption
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@customers.route('/customers/analytics', methods=['GET'])
@admin_required
def get_customer_analytics(current_user):
    """Get customer analytics and insights"""
    try:
        # Simulate analytics data
        analytics = {
            'total_customers': 100,
            'active_customers': 90,
            'new_customers_this_month': 15,
            'customer_retention_rate': 85.5,
            'average_customer_lifetime_value': 245.75,
            'loyalty_program': {
                'total_members': 78,
                'bronze_tier': 45,
                'silver_tier': 25,
                'gold_tier': 8,
                'total_points_issued': 15420,
                'total_points_redeemed': 8750
            },
            'demographics': {
                'age_groups': [
                    {'range': '18-25', 'count': 18, 'percentage': 18.0},
                    {'range': '26-35', 'count': 32, 'percentage': 32.0},
                    {'range': '36-45', 'count': 28, 'percentage': 28.0},
                    {'range': '46-55', 'count': 15, 'percentage': 15.0},
                    {'range': '56+', 'count': 7, 'percentage': 7.0}
                ],
                'communication_preferences': {
                    'email': 75,
                    'sms': 20,
                    'phone': 5
                }
            },
            'top_customers': [
                {'id': 1, 'name': 'Customer 1', 'total_spent': 1250.00, 'orders': 25},
                {'id': 15, 'name': 'Customer 15', 'total_spent': 980.50, 'orders': 18},
                {'id': 7, 'name': 'Customer 7', 'total_spent': 875.25, 'orders': 22},
                {'id': 23, 'name': 'Customer 23', 'total_spent': 750.00, 'orders': 15},
                {'id': 42, 'name': 'Customer 42', 'total_spent': 695.75, 'orders': 14}
            ],
            'monthly_trends': [
                {'month': '2024-01', 'new_customers': 12, 'total_spent': 15420.50},
                {'month': '2024-02', 'new_customers': 18, 'total_spent': 18750.25},
                {'month': '2024-03', 'new_customers': 15, 'total_spent': 16890.75},
                {'month': '2024-04', 'new_customers': 22, 'total_spent': 21340.00}
            ]
        }
        
        return jsonify(analytics), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
