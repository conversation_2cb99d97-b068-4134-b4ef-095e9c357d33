from flask import Blueprint, request, jsonify
from db.models import Order, OrderItem, Product, Table, User, db
from api.auth import token_required, admin_required
from datetime import datetime, timedelta
from sqlalchemy import func
from services.printer_service import print_receipt, test_printer, get_available_printers, set_printer

sales = Blueprint('sales', __name__)

@sales.route('/sales/daily', methods=['GET'])
@token_required
def get_daily_sales(current_user):
    """Get sales for a specific day (admin only)"""
    try:
        # Get date from query parameters, default to today
        date_str = request.args.get('date')
        
        if date_str:
            try:
                date = datetime.strptime(date_str, '%Y-%m-%d')
            except ValueError:
                return jsonify({'error': 'Invalid date format! Use YYYY-MM-DD'}), 400
        else:
            date = datetime.now()
        
        # Set start and end of the day
        start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = start_date + timedelta(days=1)
        
        # Query orders for the day
        orders = Order.query.filter(Order.date >= start_date, Order.date < end_date).all()
        
        # Calculate total sales
        total_sales = sum(order.total for order in orders)
        
        # Get sales by category
        sales_by_category = {}
        
        for order in orders:
            order_items = OrderItem.query.filter_by(order_id=order.id).all()
            
            for item in order_items:
                product = Product.query.get(item.product_id)
                
                if product:
                    if product.category not in sales_by_category:
                        sales_by_category[product.category] = 0
                    
                    sales_by_category[product.category] += product.price * item.quantity
        
        # Format sales by category for response
        categories = [{'category': category, 'total': total} for category, total in sales_by_category.items()]
        
        return jsonify({
            'date': start_date.strftime('%Y-%m-%d'),
            'total_sales': total_sales,
            'order_count': len(orders),
            'categories': categories
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@sales.route('/sales/range', methods=['GET'])
@admin_required
def get_sales_range(current_user):
    """Get sales for a date range (admin only)"""
    try:
        # Get start and end dates from query parameters
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        
        if not start_date_str or not end_date_str:
            return jsonify({'error': 'Start date and end date are required!'}), 400
        
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            
            # Set end date to end of day
            end_date = end_date.replace(hour=23, minute=59, second=59)
            
            if start_date > end_date:
                return jsonify({'error': 'Start date must be before end date!'}), 400
        except ValueError:
            return jsonify({'error': 'Invalid date format! Use YYYY-MM-DD'}), 400
        
        # Query orders for the date range
        orders = Order.query.filter(Order.date >= start_date, Order.date <= end_date).all()
        
        # Calculate total sales
        total_sales = sum(order.total for order in orders)
        
        # Get sales by day
        sales_by_day = {}
        
        for order in orders:
            day = order.date.strftime('%Y-%m-%d')
            
            if day not in sales_by_day:
                sales_by_day[day] = 0
            
            sales_by_day[day] += order.total
        
        # Format sales by day for response
        days = [{'date': day, 'total': total} for day, total in sales_by_day.items()]
        
        # Sort days by date
        days.sort(key=lambda x: x['date'])
        
        # Get sales by category
        sales_by_category = {}
        
        for order in orders:
            order_items = OrderItem.query.filter_by(order_id=order.id).all()
            
            for item in order_items:
                product = Product.query.get(item.product_id)
                
                if product:
                    if product.category not in sales_by_category:
                        sales_by_category[product.category] = 0
                    
                    sales_by_category[product.category] += product.price * item.quantity
        
        # Format sales by category for response
        categories = [{'category': category, 'total': total} for category, total in sales_by_category.items()]
        
        return jsonify({
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'total_sales': total_sales,
            'order_count': len(orders),
            'days': days,
            'categories': categories
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@sales.route('/sales/top-products', methods=['GET'])
@admin_required
def get_top_products(current_user):
    """Get top selling products (admin only)"""
    try:
        # Get limit from query parameters, default to 5
        limit = request.args.get('limit', 5, type=int)
        
        # Get date range from query parameters
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        
        if start_date_str and end_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
                
                # Set end date to end of day
                end_date = end_date.replace(hour=23, minute=59, second=59)
            except ValueError:
                return jsonify({'error': 'Invalid date format! Use YYYY-MM-DD'}), 400
        else:
            # Default to last 30 days
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
        
        # Query top products
        top_products = db.session.query(
            Product.id,
            Product.name,
            Product.category,
            func.sum(OrderItem.quantity).label('total_quantity'),
            func.sum(Product.price * OrderItem.quantity).label('total_sales')
        ).join(OrderItem, Product.id == OrderItem.product_id) \
         .join(Order, OrderItem.order_id == Order.id) \
         .filter(Order.date >= start_date, Order.date <= end_date) \
         .group_by(Product.id) \
         .order_by(func.sum(OrderItem.quantity).desc()) \
         .limit(limit) \
         .all()
        
        result = []
        
        for product in top_products:
            result.append({
                'id': product.id,
                'name': product.name,
                'category': product.category,
                'total_quantity': int(product.total_quantity),
                'total_sales': float(product.total_sales)
            })
        
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@sales.route('/print-receipt/<int:order_id>', methods=['GET'])
def print_receipt_route(order_id):
    """Generate receipt for printing"""
    try:
        order = Order.query.get(order_id)
        if not order:
            return jsonify({'error': 'Order not found'}), 404

        # Get order items
        order_items = OrderItem.query.filter_by(order_id=order_id).all()

        # Get table info
        table = Table.query.get(order.table_id)
        table_name = table.name if table else f"Table {order.table_id}"

        # Get cashier info
        cashier = User.query.get(order.cashier_id) if hasattr(order, 'cashier_id') else None
        cashier_name = cashier.username if cashier else 'System'

        receipt_data = {
            'order_id': order.id,
            'table': table_name,
            'date': order.created_at.isoformat(),
            'cashier': cashier_name,
            'total': float(order.total),
            'subtotal': float(order.total / 1.085),  # Remove tax to get subtotal
            'tax': float(order.total * 0.085 / 1.085),  # Calculate tax amount
            'discount': 0.0,  # Add discount support later
            'items': []
        }

        for item in order_items:
            product = Product.query.get(item.product_id)
            receipt_data['items'].append({
                'name': product.name if product else 'Unknown Product',
                'quantity': item.quantity,
                'price': float(item.price),
                'subtotal': float(item.quantity * item.price)
            })

        return jsonify(receipt_data), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@sales.route('/thermal-print/<int:order_id>', methods=['POST'])
@token_required
def thermal_print_receipt(current_user, order_id):
    """Print receipt to thermal printer"""
    try:
        order = Order.query.get(order_id)
        if not order:
            return jsonify({'error': 'Order not found'}), 404

        # Get order items
        order_items = OrderItem.query.filter_by(order_id=order_id).all()

        # Get table info
        table = Table.query.get(order.table_id)
        table_name = table.name if table else f"Table {order.table_id}"

        # Prepare receipt data
        receipt_data = {
            'order_id': order.id,
            'table': table_name,
            'date': order.created_at.isoformat(),
            'cashier': current_user.username,
            'total': float(order.total),
            'subtotal': float(order.total / 1.085),
            'tax': float(order.total * 0.085 / 1.085),
            'discount': 0.0,
            'items': []
        }

        for item in order_items:
            product = Product.query.get(item.product_id)
            receipt_data['items'].append({
                'name': product.name if product else 'Unknown Product',
                'quantity': item.quantity,
                'price': float(item.price),
                'subtotal': float(item.quantity * item.price)
            })

        # Print to thermal printer
        print_result = print_receipt(receipt_data)

        return jsonify(print_result), 200 if print_result['success'] else 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@sales.route('/printer/test', methods=['POST'])
@token_required
def test_thermal_printer(current_user):
    """Test thermal printer connection"""
    try:
        test_result = test_printer()
        return jsonify(test_result), 200 if test_result['success'] else 500
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@sales.route('/printer/list', methods=['GET'])
@token_required
def list_printers(current_user):
    """Get list of available printers"""
    try:
        printers = get_available_printers()
        return jsonify({'printers': printers}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@sales.route('/printer/set', methods=['POST'])
@admin_required
def set_active_printer(current_user):
    """Set the active printer"""
    try:
        data = request.get_json()
        printer_name = data.get('printer_name')

        if not printer_name:
            return jsonify({'error': 'Printer name is required'}), 400

        success = set_printer(printer_name)

        if success:
            return jsonify({'message': f'Printer set to {printer_name}'}), 200
        else:
            return jsonify({'error': 'Failed to set printer'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500
