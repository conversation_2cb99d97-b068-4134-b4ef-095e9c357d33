"""
Thermal Printer Service for Cash Register Pro
Supports ESC/POS thermal printers and Windows default printers
"""

import os
import sys
import platform
import subprocess
from datetime import datetime
from typing import Dict, List, Optional
import tempfile

class ThermalPrinter:
    """Thermal printer service with ESC/POS command support"""
    
    def __init__(self, printer_name: Optional[str] = None):
        self.printer_name = printer_name or self.get_default_printer()
        self.width = 32  # Standard thermal printer width (characters)
        self.encoding = 'cp437'  # Standard encoding for thermal printers
        
        # ESC/POS Commands
        self.ESC = b'\x1b'
        self.GS = b'\x1d'
        self.INIT = self.ESC + b'@'
        self.CUT = self.GS + b'V\x00'
        self.BOLD_ON = self.ESC + b'E\x01'
        self.BOLD_OFF = self.ESC + b'E\x00'
        self.CENTER_ON = self.ESC + b'a\x01'
        self.CENTER_OFF = self.ESC + b'a\x00'
        self.DOUBLE_HEIGHT = self.ESC + b'!\x10'
        self.NORMAL_SIZE = self.ESC + b'!\x00'
        self.LINE_FEED = b'\n'
        
    def get_default_printer(self) -> str:
        """Get the default printer name for the current OS"""
        system = platform.system()
        
        if system == "Windows":
            try:
                import win32print
                return win32print.GetDefaultPrinter()
            except ImportError:
                # Fallback method for Windows without pywin32
                try:
                    result = subprocess.run(['wmic', 'printer', 'where', 'default=true', 'get', 'name'], 
                                          capture_output=True, text=True)
                    lines = result.stdout.strip().split('\n')
                    if len(lines) > 1:
                        return lines[1].strip()
                except:
                    pass
                return "Microsoft Print to PDF"  # Fallback
                
        elif system == "Darwin":  # macOS
            try:
                result = subprocess.run(['lpstat', '-d'], capture_output=True, text=True)
                if result.stdout:
                    return result.stdout.split(':')[-1].strip()
            except:
                pass
            return "default"
            
        else:  # Linux
            try:
                result = subprocess.run(['lpstat', '-d'], capture_output=True, text=True)
                if result.stdout:
                    return result.stdout.split(':')[-1].strip()
            except:
                pass
            return "default"
    
    def format_line(self, text: str, width: int = None) -> str:
        """Format a line to fit the printer width"""
        if width is None:
            width = self.width
        return text[:width].ljust(width)
    
    def format_two_column(self, left: str, right: str, width: int = None) -> str:
        """Format text in two columns"""
        if width is None:
            width = self.width
        
        right_width = len(right)
        left_width = width - right_width
        
        if left_width < 1:
            return right[:width]
        
        return left[:left_width].ljust(left_width) + right
    
    def create_receipt_content(self, receipt_data: Dict) -> str:
        """Create formatted receipt content"""
        content = []
        
        # Header
        content.append("=" * self.width)
        content.append(self.format_line("CASH REGISTER PRO", self.width).center(self.width))
        content.append(self.format_line("Point of Sale System", self.width).center(self.width))
        content.append("=" * self.width)
        content.append("")
        
        # Receipt info
        content.append(f"Receipt #: {receipt_data.get('order_id', 'N/A')}")
        content.append(f"Table: {receipt_data.get('table', 'N/A')}")
        content.append(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content.append(f"Cashier: {receipt_data.get('cashier', 'System')}")
        content.append("")
        content.append("-" * self.width)
        
        # Items
        for item in receipt_data.get('items', []):
            name = item.get('name', 'Unknown Item')
            quantity = item.get('quantity', 1)
            price = item.get('price', 0.0)
            subtotal = quantity * price
            
            # Item name
            content.append(name[:self.width])
            
            # Quantity and price
            qty_price = f"{quantity}x ${price:.2f}"
            total_str = f"${subtotal:.2f}"
            content.append(self.format_two_column(qty_price, total_str))
            
        content.append("-" * self.width)
        
        # Totals
        subtotal = receipt_data.get('subtotal', 0.0)
        tax = receipt_data.get('tax', 0.0)
        discount = receipt_data.get('discount', 0.0)
        total = receipt_data.get('total', 0.0)
        
        if discount > 0:
            content.append(self.format_two_column("Subtotal:", f"${subtotal:.2f}"))
            content.append(self.format_two_column("Discount:", f"-${discount:.2f}"))
            content.append(self.format_two_column("Tax (8.5%):", f"${tax:.2f}"))
        else:
            content.append(self.format_two_column("Subtotal:", f"${subtotal:.2f}"))
            content.append(self.format_two_column("Tax (8.5%):", f"${tax:.2f}"))
        
        content.append("=" * self.width)
        content.append(self.format_two_column("TOTAL:", f"${total:.2f}"))
        content.append("=" * self.width)
        content.append("")
        
        # Footer
        content.append("Thank you for your business!")
        content.append("Please come again!")
        content.append("")
        content.append("Powered by Cash Register Pro")
        content.append(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        content.append("")
        content.append("")  # Extra space for cutting
        
        return "\n".join(content)
    
    def print_receipt(self, receipt_data: Dict) -> bool:
        """Print receipt to thermal printer"""
        try:
            content = self.create_receipt_content(receipt_data)
            return self.print_text(content)
        except Exception as e:
            print(f"Error printing receipt: {e}")
            return False
    
    def print_text(self, text: str) -> bool:
        """Print text to the configured printer"""
        system = platform.system()
        
        try:
            if system == "Windows":
                return self._print_windows(text)
            elif system == "Darwin":  # macOS
                return self._print_macos(text)
            else:  # Linux
                return self._print_linux(text)
        except Exception as e:
            print(f"Error printing: {e}")
            return False
    
    def _print_windows(self, text: str) -> bool:
        """Print on Windows using notepad or direct printer access"""
        try:
            # Try using win32print if available
            try:
                import win32print
                import win32api
                
                # Open printer
                printer_handle = win32print.OpenPrinter(self.printer_name)
                
                # Start document
                job_info = ("Cash Register Receipt", None, "RAW")
                job_id = win32print.StartDocPrinter(printer_handle, 1, job_info)
                win32print.StartPagePrinter(printer_handle)
                
                # Send data
                win32api.WritePrinter(printer_handle, text.encode(self.encoding))
                
                # End document
                win32print.EndPagePrinter(printer_handle)
                win32print.EndDocPrinter(printer_handle)
                win32print.ClosePrinter(printer_handle)
                
                return True
                
            except ImportError:
                # Fallback: use notepad to print
                with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                    f.write(text)
                    temp_file = f.name
                
                # Print using notepad
                subprocess.run(['notepad', '/p', temp_file], check=True)
                
                # Clean up
                os.unlink(temp_file)
                return True
                
        except Exception as e:
            print(f"Windows printing error: {e}")
            return False
    
    def _print_macos(self, text: str) -> bool:
        """Print on macOS using lpr"""
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                f.write(text)
                temp_file = f.name
            
            # Print using lpr
            cmd = ['lpr']
            if self.printer_name and self.printer_name != "default":
                cmd.extend(['-P', self.printer_name])
            cmd.append(temp_file)
            
            subprocess.run(cmd, check=True)
            
            # Clean up
            os.unlink(temp_file)
            return True
            
        except Exception as e:
            print(f"macOS printing error: {e}")
            return False
    
    def _print_linux(self, text: str) -> bool:
        """Print on Linux using lpr"""
        try:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                f.write(text)
                temp_file = f.name
            
            # Print using lpr
            cmd = ['lpr']
            if self.printer_name and self.printer_name != "default":
                cmd.extend(['-P', self.printer_name])
            cmd.append(temp_file)
            
            subprocess.run(cmd, check=True)
            
            # Clean up
            os.unlink(temp_file)
            return True
            
        except Exception as e:
            print(f"Linux printing error: {e}")
            return False
    
    def test_printer(self) -> bool:
        """Test if the printer is working"""
        test_content = f"""
PRINTER TEST
============

Printer: {self.printer_name}
Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

This is a test print from
Cash Register Pro

If you can read this, your
printer is working correctly!

============
"""
        return self.print_text(test_content.strip())
    
    def get_available_printers(self) -> List[str]:
        """Get list of available printers"""
        system = platform.system()
        printers = []
        
        try:
            if system == "Windows":
                try:
                    import win32print
                    printers = [printer[2] for printer in win32print.EnumPrinters(2)]
                except ImportError:
                    # Fallback method
                    result = subprocess.run(['wmic', 'printer', 'get', 'name'], 
                                          capture_output=True, text=True)
                    lines = result.stdout.strip().split('\n')[1:]  # Skip header
                    printers = [line.strip() for line in lines if line.strip()]
                    
            else:  # macOS and Linux
                result = subprocess.run(['lpstat', '-p'], capture_output=True, text=True)
                for line in result.stdout.split('\n'):
                    if line.startswith('printer '):
                        printer_name = line.split()[1]
                        printers.append(printer_name)
                        
        except Exception as e:
            print(f"Error getting printers: {e}")
            
        return printers if printers else ["default"]


# Global printer instance
thermal_printer = ThermalPrinter()


def print_receipt(receipt_data: Dict) -> Dict:
    """Print receipt and return status"""
    try:
        success = thermal_printer.print_receipt(receipt_data)
        return {
            'success': success,
            'message': 'Receipt printed successfully' if success else 'Failed to print receipt',
            'printer': thermal_printer.printer_name
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'Printing error: {str(e)}',
            'printer': thermal_printer.printer_name
        }


def test_printer() -> Dict:
    """Test printer functionality"""
    try:
        success = thermal_printer.test_printer()
        return {
            'success': success,
            'message': 'Printer test successful' if success else 'Printer test failed',
            'printer': thermal_printer.printer_name
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'Printer test error: {str(e)}',
            'printer': thermal_printer.printer_name
        }


def get_available_printers() -> List[str]:
    """Get list of available printers"""
    return thermal_printer.get_available_printers()


def set_printer(printer_name: str) -> bool:
    """Set the active printer"""
    try:
        thermal_printer.printer_name = printer_name
        return True
    except Exception as e:
        print(f"Error setting printer: {e}")
        return False
