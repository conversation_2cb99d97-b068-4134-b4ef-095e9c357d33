#!/usr/bin/env python3
"""
Cash Register Pro - Single File Launcher
Complete point-of-sale system with backend and frontend
"""

import os
import sys
import subprocess
import threading
import time
import webbrowser
import signal
from pathlib import Path

class CashRegisterPro:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = False
        
    def check_dependencies(self):
        """Check if required dependencies are installed"""
        print("Checking dependencies...")
        
        required_packages = [
            'flask', 'flask_sqlalchemy', 'flask_cors', 
            'jwt', 'werkzeug', 'sqlite3'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
                print(f"✓ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"✗ {package}")
        
        if missing_packages:
            print(f"\nMissing packages: {', '.join(missing_packages)}")
            print("Installing missing packages...")
            self.install_packages(missing_packages)
        
        return True
    
    def install_packages(self, packages):
        """Install missing packages"""
        package_map = {
            'flask': 'flask',
            'flask_sqlalchemy': 'flask-sqlalchemy',
            'flask_cors': 'flask-cors',
            'jwt': 'pyjwt',
            'werkzeug': 'werkzeug',
            'sqlite3': ''  # Built-in
        }
        
        for package in packages:
            if package == 'sqlite3':
                continue  # Built-in module
            
            pip_package = package_map.get(package, package)
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', pip_package], 
                             check=True, capture_output=True)
                print(f"✓ Installed {pip_package}")
            except subprocess.CalledProcessError as e:
                print(f"✗ Failed to install {pip_package}: {e}")
    
    def start_backend(self):
        """Start the Flask backend server"""
        print("Starting backend server...")
        
        backend_dir = Path(__file__).parent / "backend"
        if not backend_dir.exists():
            print("❌ Backend directory not found!")
            return False
        
        try:
            # Change to backend directory
            os.chdir(backend_dir)
            
            # Add backend to Python path
            sys.path.insert(0, str(backend_dir))
            
            # Import and run Flask app
            from app import create_app
            app = create_app()
            
            # Run in a separate thread
            def run_flask():
                app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
            
            backend_thread = threading.Thread(target=run_flask, daemon=True)
            backend_thread.start()
            
            print("✓ Backend server started on http://localhost:5000")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start backend: {e}")
            return False
    
    def start_frontend(self):
        """Start the Electron frontend"""
        print("Starting frontend application...")
        
        frontend_dir = Path(__file__).parent / "frontend"
        if not frontend_dir.exists():
            print("❌ Frontend directory not found!")
            return False
        
        try:
            # Check if we have a built Electron app
            electron_exe = frontend_dir / "dist" / "Cash Register Pro.exe"
            
            if electron_exe.exists():
                # Run the built executable
                self.frontend_process = subprocess.Popen([str(electron_exe)], cwd=frontend_dir)
                print("✓ Frontend application started")
                return True
            
            # Check if we can run with npm
            elif self.check_npm():
                os.chdir(frontend_dir)
                self.frontend_process = subprocess.Popen(['npm', 'start'], cwd=frontend_dir)
                print("✓ Frontend started with npm")
                return True
            
            else:
                # Fallback: open in browser
                print("⚠️  Electron not available, opening in browser...")
                time.sleep(3)  # Wait for backend
                webbrowser.open("http://localhost:5000")
                print("✓ Opened in default browser")
                return True
                
        except Exception as e:
            print(f"❌ Failed to start frontend: {e}")
            return False
    
    def check_npm(self):
        """Check if npm is available"""
        try:
            subprocess.run(['npm', '--version'], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            print("\n🛑 Shutting down Cash Register Pro...")
            self.shutdown()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def shutdown(self):
        """Shutdown all processes"""
        self.running = False
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
            except:
                try:
                    self.frontend_process.kill()
                except:
                    pass
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
            except:
                try:
                    self.backend_process.kill()
                except:
                    pass
    
    def run(self):
        """Main run method"""
        print("=" * 60)
        print("    💰 Cash Register Pro - Starting...")
        print("=" * 60)
        print()
        
        try:
            # Setup signal handlers
            self.setup_signal_handlers()
            
            # Check dependencies
            if not self.check_dependencies():
                print("❌ Dependency check failed")
                return False
            
            # Start backend
            if not self.start_backend():
                print("❌ Failed to start backend server")
                return False
            
            # Wait for backend to initialize
            print("⏳ Waiting for backend to initialize...")
            time.sleep(3)
            
            # Test backend connection
            try:
                import requests
                response = requests.get("http://localhost:5000/", timeout=5)
                if response.status_code == 200:
                    print("✓ Backend server is responding")
                else:
                    print("⚠️  Backend server may not be fully ready")
            except:
                print("⚠️  Could not verify backend status")
            
            # Start frontend
            if not self.start_frontend():
                print("❌ Failed to start frontend")
                return False
            
            self.running = True
            
            print()
            print("=" * 60)
            print("    🚀 Cash Register Pro is now running!")
            print("=" * 60)
            print()
            print("📍 Backend API: http://localhost:5000")
            print("🖥️  Frontend: Desktop Application")
            print()
            print("🔐 Default Login Credentials:")
            print("   Admin: admin / admin123")
            print("   Cashier: cashier / cashier123")
            print()
            print("⌨️  Press Ctrl+C to stop the application")
            print("=" * 60)
            
            # Keep the main thread alive
            try:
                while self.running:
                    time.sleep(1)
                    
                    # Check if frontend process is still running
                    if self.frontend_process and self.frontend_process.poll() is not None:
                        print("\n🔄 Frontend closed, shutting down...")
                        break
                        
            except KeyboardInterrupt:
                print("\n🛑 Received shutdown signal...")
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
        
        finally:
            self.shutdown()

def main():
    """Main entry point"""
    app = CashRegisterPro()
    success = app.run()
    
    if success:
        print("\n✅ Cash Register Pro shut down successfully")
    else:
        print("\n❌ Cash Register Pro encountered errors")
        input("Press Enter to exit...")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
