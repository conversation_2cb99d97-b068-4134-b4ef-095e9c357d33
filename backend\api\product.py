from flask import Blueprint, request, jsonify
from db.models import Product, db
from api.auth import token_required, admin_required, roles_required

product = Blueprint('product', __name__)

@product.route('/products', methods=['GET'])
@roles_required(['admin', 'cashier'])
def get_products(current_user):
    """Get all products"""
    try:
        products = Product.query.all()
        result = []
        
        for product in products:
            product_data = {
                'id': product.id,
                'name': product.name,
                'category': product.category,
                'price': product.price
            }
            result.append(product_data)
        
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@product.route('/products/<int:product_id>', methods=['GET'])
@roles_required(['admin', 'cashier'])
def get_product(current_user, product_id):
    """Get a specific product"""
    try:
        product = Product.query.get(product_id)
        
        if not product:
            return jsonify({'error': 'Product not found!'}), 404
        
        product_data = {
            'id': product.id,
            'name': product.name,
            'category': product.category,
            'price': product.price
        }
        
        return jsonify(product_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@product.route('/products', methods=['POST'])
@admin_required
def create_product(current_user):
    """Create a new product (admin only)"""
    try:
        data = request.get_json()
        
        if not data or not data.get('name') or not data.get('category') or data.get('price') is None:
            return jsonify({'error': 'Name, category, and price are required!'}), 400
        
        # Validate price
        try:
            price = float(data['price'])
            if price < 0:
                return jsonify({'error': 'Price must be a positive number!'}), 400
        except ValueError:
            return jsonify({'error': 'Price must be a valid number!'}), 400
        
        # Create new product
        new_product = Product(
            name=data['name'],
            category=data['category'],
            price=price
        )
        
        db.session.add(new_product)
        db.session.commit()
        
        return jsonify({
            'message': 'Product created successfully!',
            'product_id': new_product.id
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@product.route('/products/<int:product_id>', methods=['PUT'])
@admin_required
def update_product(current_user, product_id):
    """Update a product (admin only)"""
    try:
        data = request.get_json()
        product = Product.query.get(product_id)
        
        if not product:
            return jsonify({'error': 'Product not found!'}), 404
        
        # Update name if provided
        if data.get('name'):
            product.name = data['name']
        
        # Update category if provided
        if data.get('category'):
            product.category = data['category']
        
        # Update price if provided
        if data.get('price') is not None:
            try:
                price = float(data['price'])
                if price < 0:
                    return jsonify({'error': 'Price must be a positive number!'}), 400
                product.price = price
            except ValueError:
                return jsonify({'error': 'Price must be a valid number!'}), 400
        
        db.session.commit()
        
        return jsonify({'message': 'Product updated successfully!'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@product.route('/products/<int:product_id>', methods=['DELETE'])
@admin_required
def delete_product(current_user, product_id):
    """Delete a product (admin only)"""
    try:
        product = Product.query.get(product_id)
        
        if not product:
            return jsonify({'error': 'Product not found!'}), 404
        
        db.session.delete(product)
        db.session.commit()
        
        return jsonify({'message': 'Product deleted successfully!'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@product.route('/products/categories', methods=['GET'])
@roles_required(['admin', 'cashier'])
def get_categories(current_user):
    """Get all unique product categories"""
    try:
        # Query distinct categories
        categories = db.session.query(Product.category).distinct().all()
        result = [category[0] for category in categories]
        
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
