from flask import Flask
from flask_cors import CORS
from api.auth import auth
from api.product import product
from api.order import order
from api.table import table
from api.sales import sales
from api.inventory import inventory
from api.customers import customers
from db.models import db, init_db
from config import Config
import os

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Initialize extensions
    db.init_app(app)
    CORS(app)
    
    # Register Blueprints
    app.register_blueprint(auth, url_prefix='/api/auth')
    app.register_blueprint(product, url_prefix='/api')
    app.register_blueprint(order, url_prefix='/api')
    app.register_blueprint(table, url_prefix='/api')
    app.register_blueprint(sales, url_prefix='/api')
    app.register_blueprint(inventory, url_prefix='/api')
    app.register_blueprint(customers, url_prefix='/api')
    
    @app.route('/')
    def home():
        return {"message": "Cash Register API Running"}
    
    return app

if __name__ == '__main__':
    app = create_app()
    
    with app.app_context():
        init_db()
    
    port = int(os.environ.get('PORT', 5000))
    app.run(debug=True, host='0.0.0.0', port=port)
