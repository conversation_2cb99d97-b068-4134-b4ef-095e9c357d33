<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cash Register Pro - Sign Up</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
  <meta name="description" content="Create your Cash Register Pro account">
</head>
<body>
  <!-- Window Controls -->
  <div class="window-controls">
    <button class="window-control-btn minimize" id="minimize-btn" title="Minimize">−</button>
    <button class="window-control-btn maximize" id="maximize-btn" title="Maximize">□</button>
    <button class="window-control-btn close" id="close-btn" title="Close">×</button>
  </div>

  <!-- Theme Toggle -->
  <button class="theme-toggle" id="theme-toggle" title="Toggle Theme">
    <span class="theme-icon">🌙</span>
  </button>

  <!-- Loading Overlay -->
  <div class="loading-overlay" id="loading-overlay" style="display: none;">
    <div class="loading-spinner"></div>
    <p>Creating account...</p>
  </div>

  <!-- Signup Container -->
  <div class="login-container">
    <div class="login-form">
      <h1>🚀 Join Cash Register Pro</h1>
      
      <div class="form-group">
        <label for="username">Username</label>
        <input type="text" id="username" placeholder="Choose a username" autocomplete="username" required>
        <small class="field-hint">Must be at least 3 characters long</small>
      </div>
      
      <div class="form-group">
        <label for="email">Email (Optional)</label>
        <input type="email" id="email" placeholder="<EMAIL>" autocomplete="email">
        <small class="field-hint">For account recovery and notifications</small>
      </div>
      
      <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" placeholder="Create a strong password" autocomplete="new-password" required>
        <small class="field-hint">Must be at least 6 characters long</small>
      </div>
      
      <div class="form-group">
        <label for="confirm-password">Confirm Password</label>
        <input type="password" id="confirm-password" placeholder="Confirm your password" autocomplete="new-password" required>
        <small class="field-hint">Must match your password</small>
      </div>
      
      <div class="form-group">
        <label for="role">Account Type</label>
        <select id="role" required>
          <option value="cashier">Cashier - Process orders and manage sales</option>
          <option value="admin">Administrator - Full system access</option>
        </select>
        <small class="field-hint">Choose your role in the system</small>
      </div>
      
      <div class="form-group checkbox-group">
        <label class="checkbox-label">
          <input type="checkbox" id="terms" required>
          <span class="checkmark"></span>
          I agree to the <a href="#" class="link">Terms of Service</a> and <a href="#" class="link">Privacy Policy</a>
        </label>
      </div>
      
      <button id="signup-btn" type="submit">
        <span class="btn-text">Create Account</span>
        <div class="loading-spinner" style="display: none;"></div>
      </button>
      
      <div id="error-message"></div>
      <div id="success-message"></div>
      
      <!-- Login Link -->
      <div class="auth-switch">
        <p>Already have an account? <a href="index.html" class="link">Sign In</a></p>
      </div>
      
      <!-- Features Preview -->
      <div class="features-preview">
        <h4>What you'll get:</h4>
        <ul>
          <li>✅ Modern point-of-sale system</li>
          <li>✅ Table and order management</li>
          <li>✅ Real-time sales tracking</li>
          <li>✅ Professional receipt printing</li>
          <li>✅ Secure user authentication</li>
          <li>✅ Dark/light theme support</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Notification Container -->
  <div class="notification-container" id="notification-container"></div>

  <!-- Scripts -->
  <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
  <script src="signup.js"></script>
</body>
</html>
