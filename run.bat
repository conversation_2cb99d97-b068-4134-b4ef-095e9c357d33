@echo off
echo ========================================
echo    Cash Register Pro - Startup Script
echo ========================================
echo.

echo Starting Cash Register Pro...
echo.

echo [1/2] Starting Backend Server...
start "Cash Register Backend" cmd /k "cd backend && python app.py"
echo Backend server starting in new window...
echo.

echo [2/2] Waiting for backend to initialize...
timeout /t 3 /nobreak > nul
echo.

echo [3/3] Starting Frontend Application...
cd frontend
npm start
echo.

echo ========================================
echo Cash Register Pro is now running!
echo.
echo Backend API: http://localhost:5000
echo Frontend: Electron Desktop Application
echo.
echo To stop the application:
echo   1. Close the Electron window
echo   2. Press Ctrl+C in the backend window
echo ========================================
