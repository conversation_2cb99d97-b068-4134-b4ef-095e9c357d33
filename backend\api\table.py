from flask import Blueprint, request, jsonify
from db.models import Table, db
from api.auth import token_required, admin_required, roles_required

table = Blueprint('table', __name__)

@table.route('/tables', methods=['GET'])
@roles_required(['admin', 'cashier'])
def get_tables(current_user):
    """Get all tables"""
    try:
        tables = Table.query.all()
        result = []
        
        for table in tables:
            table_data = {
                'id': table.id,
                'name': table.name,
                'status': table.status
            }
            result.append(table_data)
        
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@table.route('/tables/<int:table_id>', methods=['GET'])
@roles_required(['admin', 'cashier'])
def get_table(current_user, table_id):
    """Get a specific table"""
    try:
        table = Table.query.get(table_id)
        
        if not table:
            return jsonify({'error': 'Table not found!'}), 404
        
        table_data = {
            'id': table.id,
            'name': table.name,
            'status': table.status
        }
        
        return jsonify(table_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@table.route('/tables', methods=['POST'])
@admin_required
def create_table(current_user):
    """Create a new table (admin only)"""
    try:
        data = request.get_json()
        
        if not data or not data.get('name'):
            return jsonify({'error': 'Table name is required!'}), 400
        
        # Set default status if not provided
        status = data.get('status', 'available')
        
        # Validate status
        if status not in ['available', 'occupied']:
            return jsonify({'error': 'Status must be either "available" or "occupied"!'}), 400
        
        # Create new table
        new_table = Table(
            name=data['name'],
            status=status
        )
        
        db.session.add(new_table)
        db.session.commit()
        
        return jsonify({
            'message': 'Table created successfully!',
            'table_id': new_table.id
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@table.route('/tables/<int:table_id>', methods=['PUT'])
@admin_required
def update_table(current_user, table_id):
    """Update a table (admin only)"""
    try:
        data = request.get_json()
        table = Table.query.get(table_id)
        
        if not table:
            return jsonify({'error': 'Table not found!'}), 404
        
        # Update name if provided
        if data.get('name'):
            table.name = data['name']
        
        # Update status if provided
        if data.get('status'):
            if data['status'] not in ['available', 'occupied']:
                return jsonify({'error': 'Status must be either "available" or "occupied"!'}), 400
            
            table.status = data['status']
        
        db.session.commit()
        
        return jsonify({'message': 'Table updated successfully!'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@table.route('/tables/<int:table_id>', methods=['DELETE'])
@admin_required
def delete_table(current_user, table_id):
    """Delete a table (admin only)"""
    try:
        table = Table.query.get(table_id)
        
        if not table:
            return jsonify({'error': 'Table not found!'}), 404
        
        # Check if table has active orders
        if table.status == 'occupied':
            return jsonify({'error': 'Cannot delete a table with active orders!'}), 400
        
        db.session.delete(table)
        db.session.commit()
        
        return jsonify({'message': 'Table deleted successfully!'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
