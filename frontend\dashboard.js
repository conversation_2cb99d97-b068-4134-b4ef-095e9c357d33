const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Enhanced Dashboard Application
class CashierDashboard {
  constructor() {
    this.token = localStorage.getItem('authToken');
    this.userRole = localStorage.getItem('userRole');
    this.username = localStorage.getItem('username');

    // State management
    this.state = {
      tables: [],
      products: [],
      categories: [],
      selectedTable: null,
      currentOrder: {
        items: [],
        subtotal: 0,
        tax: 0,
        total: 0,
        discount: 0
      },
      lastOrderId: null,
      searchTerm: '',
      selectedCategory: 'all',
      sortBy: 'name'
    };

    // DOM elements
    this.elements = {};

    // Managers
    this.notifications = window.notifications;
    this.themeManager = window.themeManager;
    this.loadingManager = window.loadingManager;

    // Tax rate (8.5%)
    this.TAX_RATE = 0.085;

    this.init();
  }

  async init() {
    try {
      // Check authentication
      if (!this.token) {
        window.location.href = 'index.html';
        return;
      }

      // Set up axios defaults
      axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;

      // Initialize DOM elements
      this.initializeElements();

      // Set up event listeners
      this.setupEventListeners();

      // Set up keyboard shortcuts
      this.setupKeyboardShortcuts();

      // Load initial data
      this.loadingManager.show('Loading dashboard...');
      await this.loadInitialData();

      // Render UI
      this.renderAll();

      // Show welcome notification
      this.notifications.show(
        'Welcome Back!',
        `Hello ${this.username}, ready to serve customers!`,
        'success'
      );

    } catch (error) {
      console.error('Dashboard initialization error:', error);
      this.notifications.show(
        'Initialization Error',
        'Failed to load dashboard. Please refresh the page.',
        'error'
      );
    } finally {
      this.loadingManager.hide();
    }
  }

  initializeElements() {
    // Window controls
    this.elements.minimizeBtn = document.getElementById('minimize-btn');
    this.elements.maximizeBtn = document.getElementById('maximize-btn');
    this.elements.closeBtn = document.getElementById('close-btn');
    this.elements.themeToggle = document.getElementById('theme-toggle');

    // Header elements
    this.elements.userInfo = document.getElementById('user-info');
    this.elements.logoutBtn = document.getElementById('logout-btn');

    // Search and filter elements
    this.elements.tableSearch = document.getElementById('table-search');
    this.elements.tableStatusFilter = document.getElementById('table-status-filter');
    this.elements.productSearch = document.getElementById('product-search');
    this.elements.productSort = document.getElementById('product-sort');

    // Main content elements
    this.elements.tablesGrid = document.getElementById('tables-grid');
    this.elements.categoryFilters = document.getElementById('category-filters');
    this.elements.productsGrid = document.getElementById('products-grid');

    // Order elements
    this.elements.selectedTable = document.getElementById('selected-table');
    this.elements.orderItems = document.getElementById('order-items');
    this.elements.orderSubtotal = document.getElementById('order-subtotal');
    this.elements.orderTax = document.getElementById('order-tax');
    this.elements.orderTotal = document.getElementById('order-total');
    this.elements.clearOrderBtn = document.getElementById('clear-order-btn');
    this.elements.placeOrderBtn = document.getElementById('place-order-btn');
    this.elements.printReceiptBtn = document.getElementById('print-receipt-btn');
    this.elements.saveDraftBtn = document.getElementById('save-draft-btn');

    // Quick action elements
    this.elements.quickActionBtns = document.querySelectorAll('.quick-action-btn');

    // Modal elements
    this.elements.discountModal = document.getElementById('discount-modal');
    this.elements.keyboardShortcuts = document.getElementById('keyboard-shortcuts');
  }

  setupEventListeners() {
    // Window controls
    if (this.elements.minimizeBtn) {
      this.elements.minimizeBtn.addEventListener('click', () => {
        ipcRenderer.invoke('window-minimize');
      });
    }

    if (this.elements.maximizeBtn) {
      this.elements.maximizeBtn.addEventListener('click', () => {
        ipcRenderer.invoke('window-maximize');
      });
    }

    if (this.elements.closeBtn) {
      this.elements.closeBtn.addEventListener('click', () => {
        ipcRenderer.invoke('window-close');
      });
    }

    // Theme toggle
    if (this.elements.themeToggle) {
      this.elements.themeToggle.addEventListener('click', () => {
        this.themeManager.toggle();
      });
    }

    // Header actions
    this.elements.logoutBtn?.addEventListener('click', () => this.logout());

    // Search and filter
    this.elements.tableSearch?.addEventListener('input', (e) => {
      this.handleTableSearch(e.target.value);
    });

    this.elements.tableStatusFilter?.addEventListener('change', (e) => {
      this.handleTableFilter(e.target.value);
    });

    this.elements.productSearch?.addEventListener('input', (e) => {
      this.handleProductSearch(e.target.value);
    });

    this.elements.productSort?.addEventListener('change', (e) => {
      this.handleProductSort(e.target.value);
    });

    // Order actions
    this.elements.clearOrderBtn?.addEventListener('click', () => this.clearOrder());
    this.elements.placeOrderBtn?.addEventListener('click', () => this.placeOrder());
    this.elements.printReceiptBtn?.addEventListener('click', () => this.printReceipt());
    this.elements.saveDraftBtn?.addEventListener('click', () => this.saveDraft());

    // Quick actions
    this.elements.quickActionBtns?.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const action = e.currentTarget.dataset.action;
        this.handleQuickAction(action);
      });
    });

    // Modal close handlers
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('close-btn') || e.target.classList.contains('modal')) {
        this.closeModals();
      }
    });
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl+N - New Order
      if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        this.clearOrder();
      }

      // Ctrl+P - Print Receipt
      if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        if (!this.elements.printReceiptBtn.disabled) {
          this.printReceipt();
        }
      }

      // Ctrl+D - Apply Discount
      if (e.ctrlKey && e.key === 'd') {
        e.preventDefault();
        this.handleQuickAction('discount');
      }

      // F1 - Show/Hide Shortcuts
      if (e.key === 'F1') {
        e.preventDefault();
        this.toggleKeyboardShortcuts();
      }

      // Escape - Clear Selection
      if (e.key === 'Escape') {
        this.clearSelection();
      }
    });
  }

  async loadInitialData() {
    try {
      // Load data in parallel
      const [tablesResponse, productsResponse] = await Promise.all([
        axios.get('http://localhost:5000/api/tables'),
        axios.get('http://localhost:5000/api/products')
      ]);

      this.state.tables = tablesResponse.data;
      this.state.products = productsResponse.data;

      // Extract categories
      this.extractCategories();

      // Update user info
      const payload = this.parseJwt(this.token);
      this.elements.userInfo.textContent = `${payload.username} (${payload.role})`;

    } catch (error) {
      console.error('Error loading initial data:', error);
      throw new Error('Failed to load dashboard data');
    }
  }

  extractCategories() {
    const categorySet = new Set();
    this.state.products.forEach(product => categorySet.add(product.category));
    this.state.categories = Array.from(categorySet);
  }

  renderAll() {
    this.renderTables();
    this.renderCategoryFilters();
    this.renderProducts();
    this.renderOrder();
  }

  renderTables() {
    if (!this.elements.tablesGrid) return;

    this.elements.tablesGrid.innerHTML = '';

    // Filter tables based on search and status
    let filteredTables = this.state.tables;

    // Apply search filter
    if (this.state.searchTerm) {
      filteredTables = filteredTables.filter(table =>
        table.name.toLowerCase().includes(this.state.searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    const statusFilter = this.elements.tableStatusFilter?.value;
    if (statusFilter) {
      filteredTables = filteredTables.filter(table => table.status === statusFilter);
    }

    filteredTables.forEach(table => {
      const tableCard = document.createElement('div');
      tableCard.className = `table-card ${table.status}`;

      if (this.state.selectedTable && this.state.selectedTable.id === table.id) {
        tableCard.classList.add('selected');
      }

      tableCard.innerHTML = `
        <div class="table-number">Table ${table.name}</div>
        <div class="table-status">${this.capitalizeFirstLetter(table.status)}</div>
        <div class="table-capacity">${table.capacity || 4} seats</div>
      `;

      tableCard.addEventListener('click', () => this.selectTable(table));
      this.elements.tablesGrid.appendChild(tableCard);
    });
  }

  renderCategoryFilters() {
    if (!this.elements.categoryFilters) return;

    this.elements.categoryFilters.innerHTML = '';

    // Add "All" filter
    const allFilter = document.createElement('div');
    allFilter.className = `category-filter ${this.state.selectedCategory === 'all' ? 'active' : ''}`;
    allFilter.setAttribute('data-category', 'all');
    allFilter.textContent = 'All Products';
    allFilter.addEventListener('click', () => this.filterByCategory('all'));
    this.elements.categoryFilters.appendChild(allFilter);

    // Add category filters
    this.state.categories.forEach(category => {
      const filterElement = document.createElement('div');
      filterElement.className = `category-filter ${this.state.selectedCategory === category ? 'active' : ''}`;
      filterElement.setAttribute('data-category', category);
      filterElement.textContent = category;
      filterElement.addEventListener('click', () => this.filterByCategory(category));
      this.elements.categoryFilters.appendChild(filterElement);
    });
  }

  renderProducts() {
    if (!this.elements.productsGrid) return;

    this.elements.productsGrid.innerHTML = '';

    // Filter and sort products
    let filteredProducts = this.getFilteredProducts();

    if (filteredProducts.length === 0) {
      this.elements.productsGrid.innerHTML = `
        <div class="no-products">
          <p>No products found matching your criteria.</p>
        </div>
      `;
      return;
    }

    filteredProducts.forEach(product => {
      const productCard = document.createElement('div');
      productCard.className = 'product-card';

      // Add out of stock class if needed
      if (product.stock !== undefined && product.stock <= 0) {
        productCard.classList.add('out-of-stock');
      }

      productCard.innerHTML = `
        <div class="product-image">${this.getProductEmoji(product.category)}</div>
        <div class="product-name">${product.name}</div>
        <div class="product-category">${product.category}</div>
        <div class="product-price">$${product.price.toFixed(2)}</div>
      `;

      if (!productCard.classList.contains('out-of-stock')) {
        productCard.addEventListener('click', () => this.addToOrder(product));
      }

      this.elements.productsGrid.appendChild(productCard);
    });
  }

  getFilteredProducts() {
    let filtered = [...this.state.products];

    // Apply category filter
    if (this.state.selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === this.state.selectedCategory);
    }

    // Apply search filter
    if (this.state.searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(this.state.searchTerm.toLowerCase()) ||
        product.category.toLowerCase().includes(this.state.searchTerm.toLowerCase())
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (this.state.sortBy) {
        case 'price':
          return a.price - b.price;
        case 'category':
          return a.category.localeCompare(b.category);
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });

    return filtered;
  }

  getProductEmoji(category) {
    const emojiMap = {
      'Food': '🍽️',
      'Drink': '🥤',
      'Dessert': '🍰',
      'Appetizer': '🥗',
      'Main Course': '🍖',
      'Beverage': '☕',
      'Alcohol': '🍷',
      'Snack': '🍿'
    };
    return emojiMap[category] || '🍽️';
  }

  selectTable(table) {
    if (table.status !== 'available' && (!this.state.selectedTable || this.state.selectedTable.id !== table.id)) {
      this.notifications.show(
        'Table Unavailable',
        `Table ${table.name} is currently ${table.status}`,
        'warning'
      );
      return;
    }

    this.state.selectedTable = table;
    this.updateSelectedTableDisplay();
    this.updateOrderButtons();
    this.renderTables();
  }

  updateSelectedTableDisplay() {
    if (!this.elements.selectedTable) return;

    if (this.state.selectedTable) {
      this.elements.selectedTable.innerHTML = `
        <div class="table-info-content">
          <span class="table-icon">🪑</span>
          <span class="table-text">Table ${this.state.selectedTable.name}</span>
        </div>
      `;
      this.elements.selectedTable.style.borderColor = 'var(--primary-color)';
    } else {
      this.elements.selectedTable.innerHTML = `
        <div class="table-info-content">
          <span class="table-icon">🪑</span>
          <span class="table-text">No table selected</span>
        </div>
      `;
      this.elements.selectedTable.style.borderColor = 'var(--border-light)';
    }
  }

  addToOrder(product) {
    if (!this.state.selectedTable) {
      this.notifications.show(
        'No Table Selected',
        'Please select a table before adding items',
        'warning'
      );
      return;
    }

    // Check if product already in order
    const existingItemIndex = this.state.currentOrder.items.findIndex(item => item.id === product.id);

    if (existingItemIndex !== -1) {
      // Increment quantity
      this.state.currentOrder.items[existingItemIndex].quantity += 1;
    } else {
      // Add new item
      this.state.currentOrder.items.push({
        id: product.id,
        name: product.name,
        price: product.price,
        quantity: 1,
        category: product.category
      });
    }

    this.calculateOrderTotals();
    this.renderOrder();
    this.updateOrderButtons();

    // Show success notification
    this.notifications.show(
      'Item Added',
      `${product.name} added to order`,
      'success',
      2000
    );
  }

  removeFromOrder(productId) {
    this.state.currentOrder.items = this.state.currentOrder.items.filter(item => item.id !== productId);
    this.calculateOrderTotals();
    this.renderOrder();
    this.updateOrderButtons();
  }

  updateQuantity(productId, newQuantity) {
    if (newQuantity <= 0) {
      this.removeFromOrder(productId);
      return;
    }

    const item = this.state.currentOrder.items.find(item => item.id === productId);
    if (item) {
      item.quantity = newQuantity;
      this.calculateOrderTotals();
      this.renderOrder();
    }
  }

  calculateOrderTotals() {
    this.state.currentOrder.subtotal = this.state.currentOrder.items.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);

    // Apply discount
    const discountAmount = this.state.currentOrder.subtotal * (this.state.currentOrder.discount / 100);
    const discountedSubtotal = this.state.currentOrder.subtotal - discountAmount;

    // Calculate tax
    this.state.currentOrder.tax = discountedSubtotal * this.TAX_RATE;

    // Calculate total
    this.state.currentOrder.total = discountedSubtotal + this.state.currentOrder.tax;
  }

  renderOrder() {
    this.renderOrderItems();
    this.renderOrderTotals();
  }

  renderOrderItems() {
    if (!this.elements.orderItems) return;

    this.elements.orderItems.innerHTML = '';

    if (this.state.currentOrder.items.length === 0) {
      this.elements.orderItems.innerHTML = `
        <div class="empty-order">
          <p>No items in order</p>
          <small>Select products to add them to the order</small>
        </div>
      `;
      return;
    }

    this.state.currentOrder.items.forEach(item => {
      const orderItem = document.createElement('div');
      orderItem.className = 'order-item';
      orderItem.innerHTML = `
        <div class="item-details">
          <div class="item-name">${item.name}</div>
          <div class="item-price">$${item.price.toFixed(2)} each</div>
        </div>
        <div class="item-quantity">
          <button class="quantity-btn decrease" data-id="${item.id}">−</button>
          <span class="quantity-display">${item.quantity}</span>
          <button class="quantity-btn increase" data-id="${item.id}">+</button>
          <button class="remove-item" data-id="${item.id}">×</button>
        </div>
      `;

      // Add event listeners
      const decreaseBtn = orderItem.querySelector('.decrease');
      const increaseBtn = orderItem.querySelector('.increase');
      const removeBtn = orderItem.querySelector('.remove-item');

      decreaseBtn.addEventListener('click', () => {
        this.updateQuantity(item.id, item.quantity - 1);
      });

      increaseBtn.addEventListener('click', () => {
        this.updateQuantity(item.id, item.quantity + 1);
      });

      removeBtn.addEventListener('click', () => {
        this.removeFromOrder(item.id);
      });

      this.elements.orderItems.appendChild(orderItem);
    });
  }

  renderOrderTotals() {
    if (this.elements.orderSubtotal) {
      this.elements.orderSubtotal.textContent = `$${this.state.currentOrder.subtotal.toFixed(2)}`;
    }

    if (this.elements.orderTax) {
      this.elements.orderTax.textContent = `$${this.state.currentOrder.tax.toFixed(2)}`;
    }

    if (this.elements.orderTotal) {
      this.elements.orderTotal.textContent = `$${this.state.currentOrder.total.toFixed(2)}`;
    }
  }
  
  updateOrderButtons() {
    const hasItems = this.state.currentOrder.items.length > 0;
    const hasTable = this.state.selectedTable !== null;

    if (this.elements.clearOrderBtn) {
      this.elements.clearOrderBtn.disabled = !hasItems;
    }

    if (this.elements.placeOrderBtn) {
      this.elements.placeOrderBtn.disabled = !hasItems || !hasTable;
    }

    if (this.elements.saveDraftBtn) {
      this.elements.saveDraftBtn.disabled = !hasItems;
    }
  }

  // Event Handlers
  handleTableSearch(searchTerm) {
    this.state.searchTerm = searchTerm;
    this.renderTables();
  }

  handleTableFilter(status) {
    this.renderTables();
  }

  handleProductSearch(searchTerm) {
    this.state.searchTerm = searchTerm;
    this.renderProducts();
  }

  handleProductSort(sortBy) {
    this.state.sortBy = sortBy;
    this.renderProducts();
  }

  filterByCategory(category) {
    this.state.selectedCategory = category;
    this.renderCategoryFilters();
    this.renderProducts();
  }

  clearOrder() {
    if (this.state.currentOrder.items.length === 0) return;

    if (confirm('Are you sure you want to clear the current order?')) {
      this.state.currentOrder = {
        items: [],
        subtotal: 0,
        tax: 0,
        total: 0,
        discount: 0
      };

      this.renderOrder();
      this.updateOrderButtons();

      this.notifications.show(
        'Order Cleared',
        'All items have been removed from the order',
        'info'
      );
    }
  }

  async placeOrder() {
    if (!this.state.selectedTable || this.state.currentOrder.items.length === 0) {
      this.notifications.show(
        'Cannot Place Order',
        'Please select a table and add items to your order',
        'error'
      );
      return;
    }

    try {
      // Show loading state
      this.elements.placeOrderBtn.disabled = true;
      const btnText = this.elements.placeOrderBtn.querySelector('span');
      const btnSpinner = this.elements.placeOrderBtn.querySelector('.loading-spinner');

      btnText.style.display = 'none';
      btnSpinner.style.display = 'inline-block';

      const orderData = {
        table_id: this.state.selectedTable.id,
        items: this.state.currentOrder.items,
        total: this.state.currentOrder.total,
        subtotal: this.state.currentOrder.subtotal,
        tax: this.state.currentOrder.tax,
        discount: this.state.currentOrder.discount
      };

      const response = await axios.post('http://localhost:5000/api/orders', orderData);
      this.state.lastOrderId = response.data.order_id;

      // Update table status
      const tableIndex = this.state.tables.findIndex(t => t.id === this.state.selectedTable.id);
      if (tableIndex !== -1) {
        this.state.tables[tableIndex].status = 'occupied';
      }

      // Reset order
      this.state.currentOrder = {
        items: [],
        subtotal: 0,
        tax: 0,
        total: 0,
        discount: 0
      };
      this.state.selectedTable = null;

      // Update UI
      this.updateSelectedTableDisplay();
      this.renderOrder();
      this.renderTables();
      this.updateOrderButtons();

      // Enable print receipt
      this.elements.printReceiptBtn.disabled = false;

      this.notifications.show(
        'Order Placed Successfully!',
        `Order #${this.state.lastOrderId} has been sent to the kitchen`,
        'success'
      );

    } catch (error) {
      console.error('Error placing order:', error);
      this.notifications.show(
        'Order Failed',
        error.response?.data?.error || 'Failed to place order. Please try again.',
        'error'
      );
    } finally {
      // Reset button state
      this.elements.placeOrderBtn.disabled = false;
      const btnText = this.elements.placeOrderBtn.querySelector('span');
      const btnSpinner = this.elements.placeOrderBtn.querySelector('.loading-spinner');

      btnText.style.display = 'inline';
      btnSpinner.style.display = 'none';
    }
  }
  
  async printReceipt() {
    if (!this.state.lastOrderId) {
      this.notifications.show(
        'No Receipt Available',
        'No recent order to print receipt for',
        'warning'
      );
      return;
    }

    try {
      const response = await axios.get(`http://localhost:5000/api/sales/print-receipt/${this.state.lastOrderId}`);
      const receipt = response.data;

      // Create enhanced receipt window
      const receiptWindow = window.open('', '_blank', 'width=400,height=600');
      receiptWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Receipt #${receipt.order_id}</title>
            <style>
              body {
                font-family: 'Courier New', monospace;
                line-height: 1.4;
                padding: 20px;
                margin: 0;
                background: white;
              }
              .header {
                text-align: center;
                margin-bottom: 20px;
                border-bottom: 2px solid #000;
                padding-bottom: 10px;
              }
              .logo { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
              .item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 5px;
              }
              .item-name { flex: 1; }
              .item-qty { width: 30px; text-align: center; }
              .item-price { width: 60px; text-align: right; }
              .total-section {
                margin-top: 15px;
                border-top: 1px solid #000;
                padding-top: 10px;
              }
              .total {
                font-weight: bold;
                border-top: 2px solid #000;
                padding-top: 5px;
                margin-top: 5px;
              }
              .footer {
                text-align: center;
                margin-top: 20px;
                border-top: 1px solid #000;
                padding-top: 10px;
              }
              @media print {
                body { padding: 10px; }
              }
            </style>
          </head>
          <body>
            <div class="header">
              <div class="logo">💰 Cash Register Pro</div>
              <div>Receipt #${receipt.order_id}</div>
              <div>Table: ${receipt.table}</div>
              <div>${new Date(receipt.date).toLocaleString()}</div>
            </div>

            <div class="items">
              ${receipt.items.map(item => `
                <div class="item">
                  <span class="item-name">${item.name}</span>
                  <span class="item-qty">${item.quantity}x</span>
                  <span class="item-price">$${(item.subtotal).toFixed(2)}</span>
                </div>
              `).join('')}
            </div>

            <div class="total-section">
              <div class="item">
                <span>Subtotal:</span>
                <span></span>
                <span>$${(receipt.total / 1.085).toFixed(2)}</span>
              </div>
              <div class="item">
                <span>Tax (8.5%):</span>
                <span></span>
                <span>$${(receipt.total * 0.085 / 1.085).toFixed(2)}</span>
              </div>
              <div class="item total">
                <span>Total:</span>
                <span></span>
                <span>$${receipt.total.toFixed(2)}</span>
              </div>
            </div>

            <div class="footer">
              <p>Thank you for your business!</p>
              <p>Visit us again soon!</p>
              <small>Powered by Cash Register Pro</small>
            </div>

            <script>
              window.onload = function() {
                window.print();
              }
            </script>
          </body>
        </html>
      `);
      receiptWindow.document.close();

      this.notifications.show(
        'Receipt Printed',
        'Receipt has been sent to printer',
        'success'
      );

    } catch (error) {
      console.error('Error printing receipt:', error);
      this.notifications.show(
        'Print Failed',
        'Failed to print receipt. Please try again.',
        'error'
      );
    }
  }

  saveDraft() {
    if (this.state.currentOrder.items.length === 0) return;

    const draft = {
      table: this.state.selectedTable,
      order: this.state.currentOrder,
      timestamp: new Date().toISOString()
    };

    localStorage.setItem('orderDraft', JSON.stringify(draft));

    this.notifications.show(
      'Draft Saved',
      'Order has been saved as draft',
      'success'
    );
  }

  handleQuickAction(action) {
    switch (action) {
      case 'discount':
        this.showDiscountModal();
        break;
      case 'split-bill':
        this.showSplitBillModal();
        break;
      case 'customer-info':
        this.showCustomerInfoModal();
        break;
    }
  }

  showDiscountModal() {
    if (this.elements.discountModal) {
      this.elements.discountModal.style.display = 'block';
    }
  }

  showSplitBillModal() {
    this.notifications.show(
      'Feature Coming Soon',
      'Split bill functionality will be available in the next update',
      'info'
    );
  }

  showCustomerInfoModal() {
    this.notifications.show(
      'Feature Coming Soon',
      'Customer management will be available in the next update',
      'info'
    );
  }

  closeModals() {
    if (this.elements.discountModal) {
      this.elements.discountModal.style.display = 'none';
    }
  }

  toggleKeyboardShortcuts() {
    if (this.elements.keyboardShortcuts) {
      const isVisible = this.elements.keyboardShortcuts.style.display !== 'none';
      this.elements.keyboardShortcuts.style.display = isVisible ? 'none' : 'block';
    }
  }

  clearSelection() {
    this.state.selectedTable = null;
    this.updateSelectedTableDisplay();
    this.renderTables();
    this.updateOrderButtons();
  }

  logout() {
    if (this.state.currentOrder.items.length > 0) {
      if (!confirm('You have items in your current order. Are you sure you want to logout?')) {
        return;
      }
    }

    localStorage.removeItem('authToken');
    localStorage.removeItem('userRole');
    localStorage.removeItem('username');
    localStorage.removeItem('orderDraft');

    this.notifications.show(
      'Logged Out',
      'You have been successfully logged out',
      'info'
    );

    setTimeout(() => {
      window.location.href = 'index.html';
    }, 1000);
  }

  // Utility functions
  parseJwt(token) {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));

      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error parsing JWT:', error);
      return {};
    }
  }

  capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }
}

// Initialize the dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new CashierDashboard();
});



