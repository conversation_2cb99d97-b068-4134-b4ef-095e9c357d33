const { ipc<PERSON>enderer } = require('electron');

// Import global utilities from renderer.js
const notifications = window.notifications || {
  show: (title, message, type) => {
    console.log(`${type.toUpperCase()}: ${title} - ${message}`);
  }
};

const themeManager = window.themeManager || {
  toggle: () => {
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
  }
};

const loadingManager = window.loadingManager || {
  show: (message) => {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
      overlay.querySelector('p').textContent = message || 'Loading...';
      overlay.style.display = 'flex';
    }
  },
  hide: () => {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
      overlay.style.display = 'none';
    }
  }
};

// Window controls
function setupWindowControls() {
  const minimizeBtn = document.getElementById('minimize-btn');
  const maximizeBtn = document.getElementById('maximize-btn');
  const closeBtn = document.getElementById('close-btn');

  if (minimizeBtn) {
    minimizeBtn.addEventListener('click', () => {
      ipcRenderer.invoke('window-minimize');
    });
  }

  if (maximizeBtn) {
    maximizeBtn.addEventListener('click', () => {
      ipcRenderer.invoke('window-maximize');
    });
  }

  if (closeBtn) {
    closeBtn.addEventListener('click', () => {
      ipcRenderer.invoke('window-close');
    });
  }
}

// Theme management
function setupTheme() {
  const currentTheme = localStorage.getItem('theme') || 'light';
  document.documentElement.setAttribute('data-theme', currentTheme);
  
  const themeIcon = document.querySelector('.theme-icon');
  if (themeIcon) {
    themeIcon.textContent = currentTheme === 'light' ? '🌙' : '☀️';
  }

  const themeToggle = document.getElementById('theme-toggle');
  if (themeToggle) {
    themeToggle.addEventListener('click', () => {
      themeManager.toggle();
      const newTheme = document.documentElement.getAttribute('data-theme');
      themeIcon.textContent = newTheme === 'light' ? '🌙' : '☀️';
    });
  }
}

// Form validation
function validateForm(formData) {
  const errors = [];
  
  if (!formData.username || formData.username.length < 3) {
    errors.push('Username must be at least 3 characters long');
  }
  
  if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
    errors.push('Username can only contain letters, numbers, and underscores');
  }
  
  if (!formData.password || formData.password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }
  
  if (formData.password !== formData.confirmPassword) {
    errors.push('Passwords do not match');
  }
  
  if (!formData.role || !['admin', 'cashier'].includes(formData.role)) {
    errors.push('Please select a valid account type');
  }
  
  if (!formData.terms) {
    errors.push('You must agree to the Terms of Service and Privacy Policy');
  }
  
  // Email validation (if provided)
  if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.push('Please enter a valid email address');
  }
  
  return errors;
}

// Real-time validation
function setupRealTimeValidation() {
  const usernameInput = document.getElementById('username');
  const passwordInput = document.getElementById('password');
  const confirmPasswordInput = document.getElementById('confirm-password');
  const emailInput = document.getElementById('email');

  // Username validation
  usernameInput.addEventListener('input', () => {
    const value = usernameInput.value;
    const isValid = value.length >= 3 && /^[a-zA-Z0-9_]+$/.test(value);
    
    usernameInput.style.borderColor = value.length === 0 ? '' : 
      isValid ? 'var(--success-color)' : 'var(--danger-color)';
  });

  // Password strength indicator
  passwordInput.addEventListener('input', () => {
    const value = passwordInput.value;
    const strength = getPasswordStrength(value);
    
    passwordInput.style.borderColor = value.length === 0 ? '' : 
      strength >= 3 ? 'var(--success-color)' : 
      strength >= 2 ? 'var(--warning-color)' : 'var(--danger-color)';
  });

  // Confirm password validation
  confirmPasswordInput.addEventListener('input', () => {
    const password = passwordInput.value;
    const confirmPassword = confirmPasswordInput.value;
    
    if (confirmPassword.length > 0) {
      confirmPasswordInput.style.borderColor = 
        password === confirmPassword ? 'var(--success-color)' : 'var(--danger-color)';
    }
  });

  // Email validation
  emailInput.addEventListener('input', () => {
    const value = emailInput.value;
    if (value.length > 0) {
      const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      emailInput.style.borderColor = isValid ? 'var(--success-color)' : 'var(--danger-color)';
    }
  });
}

function getPasswordStrength(password) {
  let strength = 0;
  
  if (password.length >= 6) strength++;
  if (password.length >= 8) strength++;
  if (/[A-Z]/.test(password)) strength++;
  if (/[a-z]/.test(password)) strength++;
  if (/[0-9]/.test(password)) strength++;
  if (/[^A-Za-z0-9]/.test(password)) strength++;
  
  return strength;
}

// Main signup functionality
document.addEventListener('DOMContentLoaded', () => {
  setupWindowControls();
  setupTheme();
  setupRealTimeValidation();
  
  const signupBtn = document.getElementById('signup-btn');
  const usernameInput = document.getElementById('username');
  const emailInput = document.getElementById('email');
  const passwordInput = document.getElementById('password');
  const confirmPasswordInput = document.getElementById('confirm-password');
  const roleSelect = document.getElementById('role');
  const termsCheckbox = document.getElementById('terms');
  const errorMessage = document.getElementById('error-message');
  const successMessage = document.getElementById('success-message');
  const btnText = signupBtn.querySelector('.btn-text');
  const btnSpinner = signupBtn.querySelector('.loading-spinner');
  
  // Auto-focus username field
  if (usernameInput) {
    usernameInput.focus();
  }
  
  // Form submission
  const handleSignup = async () => {
    const formData = {
      username: usernameInput.value.trim(),
      email: emailInput.value.trim(),
      password: passwordInput.value,
      confirmPassword: confirmPasswordInput.value,
      role: roleSelect.value,
      terms: termsCheckbox.checked
    };
    
    // Clear previous messages
    errorMessage.textContent = '';
    successMessage.textContent = '';
    
    // Validate form
    const errors = validateForm(formData);
    if (errors.length > 0) {
      errorMessage.textContent = errors[0];
      notifications.show('Validation Error', errors[0], 'error');
      return;
    }
    
    await signup(formData);
  };
  
  // Event listeners
  signupBtn.addEventListener('click', handleSignup);
  
  // Enter key support for all fields
  [usernameInput, emailInput, passwordInput, confirmPasswordInput].forEach(input => {
    input.addEventListener('keypress', (event) => {
      if (event.key === 'Enter') {
        handleSignup();
      }
    });
  });
  
  // Real-time validation feedback
  [usernameInput, emailInput, passwordInput, confirmPasswordInput].forEach(input => {
    input.addEventListener('input', () => {
      if (errorMessage.textContent) {
        errorMessage.textContent = '';
      }
    });
  });
  
  async function signup(formData) {
    try {
      // Show loading state
      signupBtn.disabled = true;
      btnText.style.display = 'none';
      btnSpinner.style.display = 'inline-block';
      loadingManager.show('Creating your account...');
      
      const requestData = {
        username: formData.username,
        password: formData.password,
        role: formData.role
      };
      
      // Add email if provided
      if (formData.email) {
        requestData.email = formData.email;
      }
      
      const response = await axios.post('http://localhost:5000/api/auth/signup', requestData, {
        timeout: 10000 // 10 second timeout
      });
      
      if (response.data.token) {
        // Store auth data
        localStorage.setItem('authToken', response.data.token);
        localStorage.setItem('userRole', response.data.role);
        localStorage.setItem('username', response.data.username);
        
        // Show success message
        successMessage.textContent = 'Account created successfully! Redirecting...';
        notifications.show(
          'Welcome to Cash Register Pro!', 
          `Account created successfully. Welcome, ${response.data.username}!`, 
          'success'
        );
        
        // Redirect based on role
        setTimeout(() => {
          if (response.data.role === 'admin') {
            window.location.href = 'admin-dashboard.html';
          } else {
            window.location.href = 'cashier-dashboard.html';
          }
        }, 2000);
      }
    } catch (error) {
      console.error('Signup error:', error);
      
      let errorMsg = 'Account creation failed. Please try again.';
      
      if (error.code === 'ECONNREFUSED') {
        errorMsg = 'Cannot connect to server. Please ensure the backend is running.';
      } else if (error.response?.data?.error) {
        errorMsg = error.response.data.error;
      } else if (error.code === 'ECONNABORTED') {
        errorMsg = 'Request timeout. Please check your connection.';
      }
      
      errorMessage.textContent = errorMsg;
      notifications.show('Signup Failed', errorMsg, 'error');
      
      // Shake animation for form
      document.querySelector('.login-form').style.animation = 'shake 0.5s ease-in-out';
      setTimeout(() => {
        document.querySelector('.login-form').style.animation = '';
      }, 500);
      
    } finally {
      // Reset loading state
      signupBtn.disabled = false;
      btnText.style.display = 'inline';
      btnSpinner.style.display = 'none';
      loadingManager.hide();
    }
  }
});

// Global error handler
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
  notifications.show(
    'Application Error', 
    'An unexpected error occurred. Please refresh the application.', 
    'error'
  );
});
