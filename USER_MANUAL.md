# 📖 Cash Register Pro - User Manual

## 🚀 Getting Started

### System Requirements
- **Operating System**: Windows 10/11, macOS 10.14+, or Linux Ubuntu 18.04+
- **RAM**: Minimum 4GB, Recommended 8GB
- **Storage**: 500MB free space
- **Network**: Internet connection for initial setup

### First Time Setup

1. **Start the Application**
   - Double-click the Cash Register Pro icon
   - The application will open with a login screen

2. **Login Credentials**
   - **Administrator**: Username: `admin`, Password: `admin123`
   - **Cashier**: Username: `cashier`, Password: `cashier123`

3. **Theme Selection**
   - Click the moon/sun icon in the top-left to toggle between dark and light themes
   - Your preference will be saved automatically

## 🏪 Cashier Operations

### Dashboard Overview
The cashier dashboard is divided into three main sections:

1. **Tables Section (Left)**: Shows all available tables
2. **Products Section (Center)**: Displays menu items by category
3. **Order Section (Right)**: Current order details and actions

### Taking an Order

#### Step 1: Select a Table
- Available tables are shown with a **green** border
- Occupied tables have a **red** border
- Reserved tables have a **yellow** border
- Click on an available table to select it

#### Step 2: Add Products to Order
- Browse products by category using the filter buttons
- Use the search box to quickly find specific items
- Click on any product to add it to the current order
- Products are automatically added with quantity 1

#### Step 3: Manage Order Items
- **Increase Quantity**: Click the `+` button next to any item
- **Decrease Quantity**: Click the `-` button next to any item
- **Remove Item**: Click the `×` button to remove an item completely
- **Clear All**: Click "Clear All" to empty the entire order

#### Step 4: Review Order Totals
- **Subtotal**: Total before tax
- **Tax (8.5%)**: Automatically calculated tax amount
- **Total**: Final amount including tax

#### Step 5: Place the Order
- Click "Place Order" to submit the order to the kitchen
- The table status will change to "Occupied"
- You'll receive a confirmation notification

#### Step 6: Print Receipt
- After placing an order, the "Print Receipt" button becomes active
- Click to generate and print a customer receipt
- The receipt opens in a new window and automatically triggers printing

### Quick Actions

#### Discount Application
- Click the "💰 Discount" quick action button
- Choose between percentage or fixed amount discount
- Enter the discount value
- Click "Apply Discount" to update the order total

#### Keyboard Shortcuts
- **Ctrl + N**: Start a new order (clears current order)
- **Ctrl + P**: Print receipt for the last order
- **Ctrl + D**: Open discount dialog
- **F1**: Show/hide keyboard shortcuts help
- **Esc**: Clear table selection

### Search and Filtering

#### Product Search
- Use the search box in the products section
- Search by product name or category
- Results update in real-time as you type

#### Table Search
- Use the search box in the tables section
- Find tables by name or number
- Filter by status (Available, Occupied, Reserved)

#### Category Filtering
- Click category buttons to filter products
- "All Products" shows the complete menu
- Categories are automatically generated from your product catalog

## 🔧 Advanced Features

### Order Management
- **Save Draft**: Save incomplete orders for later completion
- **Load Draft**: Restore previously saved draft orders
- **Split Bill**: Divide orders between multiple payments (coming soon)

### Customer Management
- **Customer Info**: Add customer details to orders (coming soon)
- **Loyalty Program**: Track customer visits and rewards (coming soon)

### Reporting
- **Daily Sales**: View sales totals by day
- **Product Performance**: See best-selling items
- **Table Turnover**: Monitor table usage statistics

## 🎨 Customization

### Theme Options
- **Light Theme**: Clean, bright interface for daytime use
- **Dark Theme**: Easy on the eyes for evening shifts
- **Auto Theme**: Follows system preference (coming soon)

### Display Settings
- **Font Size**: Adjust text size for better readability
- **Color Contrast**: High contrast mode for accessibility
- **Animation Speed**: Reduce motion for sensitive users

## 🔒 Security Features

### User Authentication
- Secure login with encrypted passwords
- Session timeout for security
- Role-based access control

### Data Protection
- Local database encryption
- Automatic backups
- Audit trail for all transactions

## 🛠️ Troubleshooting

### Common Issues

#### Application Won't Start
1. Check if backend server is running
2. Verify all dependencies are installed
3. Restart the application
4. Check system requirements

#### Login Problems
1. Verify username and password
2. Check caps lock status
3. Try default credentials
4. Contact administrator for password reset

#### Order Not Saving
1. Ensure table is selected
2. Check network connection
3. Verify at least one item is in order
4. Try refreshing the application

#### Receipt Not Printing
1. Check printer connection
2. Verify printer drivers are installed
3. Ensure printer has paper
4. Try printing from another application

### Performance Tips
- Close unused applications to free memory
- Restart the application daily
- Keep the database optimized
- Update to the latest version regularly

## 📞 Support

### Getting Help
- **F1 Key**: Quick help and shortcuts
- **User Manual**: This comprehensive guide
- **Video Tutorials**: Available online
- **Technical Support**: Contact your system administrator

### Reporting Issues
When reporting problems, please include:
- Operating system version
- Application version
- Steps to reproduce the issue
- Error messages (if any)
- Screenshots (if helpful)

## 📈 Best Practices

### Daily Operations
1. Start each shift by checking table status
2. Verify product availability
3. Test receipt printer
4. Review previous shift notes

### Order Accuracy
1. Always confirm order with customer
2. Double-check quantities
3. Verify special requests
4. Review total before processing

### Customer Service
1. Greet customers promptly
2. Explain menu items when asked
3. Process orders efficiently
4. Thank customers for their business

### End of Shift
1. Complete all pending orders
2. Print daily sales report
3. Clear draft orders
4. Log out securely

## 🔄 Updates and Maintenance

### Automatic Updates
- The application checks for updates automatically
- Updates are downloaded in the background
- You'll be notified when updates are ready
- Restart to apply updates

### Manual Updates
1. Check for updates in the help menu
2. Download the latest version
3. Close the application
4. Install the update
5. Restart the application

### Data Backup
- Orders are automatically backed up
- Export data regularly
- Store backups in a secure location
- Test restore procedures periodically

---

**Need more help?** Contact your system administrator or refer to the online documentation.

**Cash Register Pro** - Your complete point-of-sale solution! 🚀
