from flask import Blueprint, request, jsonify
from db.models import Order, OrderItem, Product, Table, db
from api.auth import token_required, admin_required, roles_required
from datetime import datetime

order = Blueprint('order', __name__)

@order.route('/orders', methods=['GET'])
@roles_required(['admin', 'cashier'])
def get_orders(current_user):
    """Get all orders"""
    try:
        # Get status filter from query parameters
        status = request.args.get('status')
        
        # Base query
        query = Order.query
        
        # Apply status filter if provided
        if status:
            query = query.filter_by(status=status)
        
        # Get orders
        orders = query.order_by(Order.date.desc()).all()
        result = []
        
        for order in orders:
            # Get table name
            table = Table.query.get(order.table_id)
            table_name = table.name if table else "Unknown"
            
            # Get order items
            items = OrderItem.query.filter_by(order_id=order.id).all()
            order_items = []
            
            for item in items:
                product = Product.query.get(item.product_id)
                
                if product:
                    order_items.append({
                        'id': item.id,
                        'product_id': item.product_id,
                        'product_name': product.name,
                        'quantity': item.quantity,
                        'price': product.price,
                        'subtotal': product.price * item.quantity
                    })
            
            order_data = {
                'id': order.id,
                'table_id': order.table_id,
                'table_name': table_name,
                'date': order.date.strftime('%Y-%m-%d %H:%M:%S'),
                'total': order.total,
                'status': order.status,
                'items': order_items
            }
            
            result.append(order_data)
        
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@order.route('/orders/<int:order_id>', methods=['GET'])
@roles_required(['admin', 'cashier'])
def get_order(current_user, order_id):
    """Get a specific order"""
    try:
        order = Order.query.get(order_id)
        
        if not order:
            return jsonify({'error': 'Order not found!'}), 404
        
        # Get table name
        table = Table.query.get(order.table_id)
        table_name = table.name if table else "Unknown"
        
        # Get order items
        items = OrderItem.query.filter_by(order_id=order.id).all()
        order_items = []
        
        for item in items:
            product = Product.query.get(item.product_id)
            
            if product:
                order_items.append({
                    'id': item.id,
                    'product_id': item.product_id,
                    'product_name': product.name,
                    'quantity': item.quantity,
                    'price': product.price,
                    'subtotal': product.price * item.quantity
                })
        
        order_data = {
            'id': order.id,
            'table_id': order.table_id,
            'table_name': table_name,
            'date': order.date.strftime('%Y-%m-%d %H:%M:%S'),
            'total': order.total,
            'status': order.status,
            'items': order_items
        }
        
        return jsonify(order_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@order.route('/orders', methods=['POST'])
@roles_required(['admin', 'cashier'])
def create_order(current_user):
    """Create a new order"""
    try:
        data = request.get_json()
        
        if not data or not data.get('table_id') or not data.get('items') or data.get('total') is None:
            return jsonify({'error': 'Table ID, items, and total are required!'}), 400
        
        # Check if table exists and is available
        table = Table.query.get(data['table_id'])
        
        if not table:
            return jsonify({'error': 'Table not found!'}), 404
        
        if table.status == 'occupied':
            return jsonify({'error': 'Table is already occupied!'}), 400
        
        # Validate items
        items = data['items']
        
        if not isinstance(items, list) or len(items) == 0:
            return jsonify({'error': 'Items must be a non-empty list!'}), 400
        
        # Validate each item
        for item in items:
            if not item.get('id') or not item.get('quantity'):
                return jsonify({'error': 'Each item must have an ID and quantity!'}), 400
            
            # Check if product exists
            product = Product.query.get(item['id'])
            
            if not product:
                return jsonify({'error': f'Product with ID {item["id"]} not found!'}), 404
            
            # Validate quantity
            if not isinstance(item['quantity'], int) or item['quantity'] <= 0:
                return jsonify({'error': 'Quantity must be a positive integer!'}), 400
        
        # Create new order
        new_order = Order(
            table_id=data['table_id'],
            total=data['total'],
            status='pending'
        )
        
        db.session.add(new_order)
        db.session.flush()  # Get the order ID without committing
        
        # Create order items
        for item in items:
            new_item = OrderItem(
                order_id=new_order.id,
                product_id=item['id'],
                quantity=item['quantity']
            )
            
            db.session.add(new_item)
        
        # Update table status
        table.status = 'occupied'
        
        db.session.commit()
        
        return jsonify({
            'message': 'Order created successfully!',
            'order_id': new_order.id
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@order.route('/orders/<int:order_id>', methods=['PUT'])
@roles_required(['admin', 'cashier'])
def update_order(current_user, order_id):
    """Update an order"""
    try:
        data = request.get_json()
        order = Order.query.get(order_id)
        
        if not order:
            return jsonify({'error': 'Order not found!'}), 404
        
        # Update status if provided
        if data.get('status'):
            if data['status'] not in ['pending', 'completed', 'cancelled']:
                return jsonify({'error': 'Status must be "pending", "completed", or "cancelled"!'}), 400
            
            old_status = order.status
            order.status = data['status']
            
            # If order is completed or cancelled, free up the table
            if data['status'] in ['completed', 'cancelled'] and old_status == 'pending':
                table = Table.query.get(order.table_id)
                
                if table:
                    table.status = 'available'
        
        # Update items if provided
        if data.get('items'):
            items = data['items']
            
            if not isinstance(items, list):
                return jsonify({'error': 'Items must be a list!'}), 400
            
            # Delete existing items
            OrderItem.query.filter_by(order_id=order.id).delete()
            
            # Create new items
            for item in items:
                if not item.get('id') or not item.get('quantity'):
                    return jsonify({'error': 'Each item must have an ID and quantity!'}), 400
                
                # Check if product exists
                product = Product.query.get(item['id'])
                
                if not product:
                    return jsonify({'error': f'Product with ID {item["id"]} not found!'}), 404
                
                # Validate quantity
                if not isinstance(item['quantity'], int) or item['quantity'] <= 0:
                    return jsonify({'error': 'Quantity must be a positive integer!'}), 400
                
                new_item = OrderItem(
                    order_id=order.id,
                    product_id=item['id'],
                    quantity=item['quantity']
                )
                
                db.session.add(new_item)
            
            # Update total if provided
            if data.get('total') is not None:
                order.total = data['total']
        
        db.session.commit()
        
        return jsonify({'message': 'Order updated successfully!'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@order.route('/orders/<int:order_id>', methods=['DELETE'])
@admin_required
def delete_order(current_user, order_id):
    """Delete an order (admin only)"""
    try:
        order = Order.query.get(order_id)
        
        if not order:
            return jsonify({'error': 'Order not found!'}), 404
        
        # If order is pending, free up the table
        if order.status == 'pending':
            table = Table.query.get(order.table_id)
            
            if table:
                table.status = 'available'
        
        # Delete order items
        OrderItem.query.filter_by(order_id=order.id).delete()
        
        # Delete order
        db.session.delete(order)
        db.session.commit()
        
        return jsonify({'message': 'Order deleted successfully!'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
