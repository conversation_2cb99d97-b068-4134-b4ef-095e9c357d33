#!/usr/bin/env python3
"""
Cash Register Pro - Single Executable Builder
Creates a single .exe file that contains both backend and frontend
"""

import os
import sys
import shutil
import subprocess
import tempfile
import zipfile
from pathlib import Path

def create_launcher_script():
    """Create a launcher script that starts both backend and frontend"""
    launcher_content = '''
import os
import sys
import subprocess
import threading
import time
import tempfile
import zipfile
from pathlib import Path
import webbrowser

def extract_resources():
    """Extract embedded resources to temp directory"""
    # Get the directory where the executable is located
    if getattr(sys, 'frozen', False):
        # Running as compiled executable
        exe_dir = Path(sys.executable).parent
        resource_dir = exe_dir / "resources"
    else:
        # Running as script
        exe_dir = Path(__file__).parent
        resource_dir = exe_dir
    
    # Create temp directory for extracted files
    temp_dir = Path(tempfile.mkdtemp(prefix="cashregister_"))
    
    # Extract backend files
    backend_zip = resource_dir / "backend.zip"
    if backend_zip.exists():
        with zipfile.ZipFile(backend_zip, 'r') as zip_ref:
            zip_ref.extractall(temp_dir / "backend")
    
    # Extract frontend files
    frontend_zip = resource_dir / "frontend.zip"
    if frontend_zip.exists():
        with zipfile.ZipFile(frontend_zip, 'r') as zip_ref:
            zip_ref.extractall(temp_dir / "frontend")
    
    return temp_dir

def start_backend(temp_dir):
    """Start the Flask backend server"""
    backend_dir = temp_dir / "backend"
    os.chdir(backend_dir)
    
    # Add backend directory to Python path
    sys.path.insert(0, str(backend_dir))
    
    try:
        # Import and run the Flask app
        from app import app
        app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
    except Exception as e:
        print(f"Error starting backend: {e}")
        input("Press Enter to exit...")
        sys.exit(1)

def start_frontend(temp_dir):
    """Start the Electron frontend"""
    frontend_dir = temp_dir / "frontend"
    
    try:
        # Check if we have a pre-built Electron app
        electron_exe = frontend_dir / "dist" / "Cash Register Pro.exe"
        if electron_exe.exists():
            subprocess.run([str(electron_exe)], cwd=frontend_dir)
        else:
            # Fallback: try to run with npm/electron
            if shutil.which("npm"):
                subprocess.run(["npm", "start"], cwd=frontend_dir)
            else:
                # Open in default browser as fallback
                time.sleep(3)  # Wait for backend to start
                webbrowser.open("http://localhost:5000")
                input("Backend is running at http://localhost:5000\\nPress Enter to stop...")
                
    except Exception as e:
        print(f"Error starting frontend: {e}")
        # Fallback to browser
        time.sleep(3)
        webbrowser.open("http://localhost:5000")
        input("Backend is running at http://localhost:5000\\nPress Enter to stop...")

def main():
    """Main launcher function"""
    print("=" * 50)
    print("    Cash Register Pro - Starting...")
    print("=" * 50)
    
    try:
        # Extract resources
        print("Extracting application files...")
        temp_dir = extract_resources()
        
        # Start backend in a separate thread
        print("Starting backend server...")
        backend_thread = threading.Thread(target=start_backend, args=(temp_dir,), daemon=True)
        backend_thread.start()
        
        # Wait a moment for backend to start
        time.sleep(3)
        
        # Start frontend
        print("Starting frontend application...")
        start_frontend(temp_dir)
        
    except KeyboardInterrupt:
        print("\\nShutting down Cash Register Pro...")
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")
    finally:
        # Cleanup temp directory
        try:
            shutil.rmtree(temp_dir, ignore_errors=True)
        except:
            pass

if __name__ == "__main__":
    main()
'''
    
    with open("launcher.py", "w") as f:
        f.write(launcher_content.strip())

def create_resource_zips():
    """Create zip files for backend and frontend"""
    print("Creating resource archives...")
    
    # Create backend.zip
    with zipfile.ZipFile("backend.zip", "w", zipfile.ZIP_DEFLATED) as zip_file:
        backend_dir = Path("backend")
        for file_path in backend_dir.rglob("*"):
            if file_path.is_file() and not file_path.name.endswith(('.pyc', '.pyo')):
                arcname = file_path.relative_to(backend_dir.parent)
                zip_file.write(file_path, arcname)
    
    # Create frontend.zip
    with zipfile.ZipFile("frontend.zip", "w", zipfile.ZIP_DEFLATED) as zip_file:
        frontend_dir = Path("frontend")
        for file_path in frontend_dir.rglob("*"):
            if file_path.is_file() and not file_path.name.startswith('.'):
                # Skip node_modules and other large directories
                if "node_modules" not in str(file_path) and "dist" not in str(file_path):
                    arcname = file_path.relative_to(frontend_dir.parent)
                    zip_file.write(file_path, arcname)

def install_dependencies():
    """Install required Python packages"""
    print("Installing Python dependencies...")

    packages = [
        "flask",
        "flask-sqlalchemy",
        "flask-cors",
        "pyjwt",
        "werkzeug",
        "python-dotenv",
        "pyinstaller",
        "requests",
        "pywin32;platform_system=='Windows'"
    ]
    
    for package in packages:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                         check=True, capture_output=True)
            print(f"✓ Installed {package}")
        except subprocess.CalledProcessError as e:
            print(f"✗ Failed to install {package}: {e}")

def build_executable():
    """Build the single executable using PyInstaller"""
    print("Building executable with PyInstaller...")
    
    # PyInstaller command
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed",
        "--name", "CashRegisterPro",
        "--icon", "frontend/assets/icon.ico" if os.path.exists("frontend/assets/icon.ico") else None,
        "--add-data", "backend.zip;.",
        "--add-data", "frontend.zip;.",
        "--hidden-import", "flask",
        "--hidden-import", "flask_sqlalchemy",
        "--hidden-import", "flask_cors",
        "--hidden-import", "jwt",
        "--hidden-import", "werkzeug",
        "--hidden-import", "sqlite3",
        "launcher.py"
    ]
    
    # Remove None values
    cmd = [arg for arg in cmd if arg is not None]
    
    try:
        subprocess.run(cmd, check=True)
        print("✓ Executable built successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to build executable: {e}")
        return False

def main():
    """Main build process"""
    print("=" * 60)
    print("    Cash Register Pro - Executable Builder")
    print("=" * 60)
    
    try:
        # Check if we're in the right directory
        if not os.path.exists("backend") or not os.path.exists("frontend"):
            print("Error: Please run this script from the project root directory")
            print("Expected structure:")
            print("  - backend/")
            print("  - frontend/")
            return False
        
        # Install dependencies
        install_dependencies()
        
        # Create launcher script
        print("Creating launcher script...")
        create_launcher_script()
        
        # Create resource archives
        create_resource_zips()
        
        # Build executable
        if build_executable():
            print("\\n" + "=" * 60)
            print("    BUILD SUCCESSFUL!")
            print("=" * 60)
            print("\\nExecutable created: dist/CashRegisterPro.exe")
            print("\\nTo run the application:")
            print("1. Copy CashRegisterPro.exe to any location")
            print("2. Double-click to run")
            print("3. The application will start automatically")
            print("\\nFeatures included:")
            print("✓ Complete backend API server")
            print("✓ Modern frontend interface")
            print("✓ Thermal printer support")
            print("✓ Local SQLite database")
            print("✓ User authentication system")
            print("✓ All POS functionality")
            return True
        else:
            print("\\nBuild failed. Please check the errors above.")
            return False
            
    except Exception as e:
        print(f"\\nBuild error: {e}")
        return False
    finally:
        # Cleanup temporary files
        for temp_file in ["launcher.py", "backend.zip", "frontend.zip"]:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass

if __name__ == "__main__":
    success = main()
    input("\\nPress Enter to exit...")
    sys.exit(0 if success else 1)
