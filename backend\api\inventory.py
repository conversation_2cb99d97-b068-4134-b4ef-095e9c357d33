"""
Inventory Management API for Cash Register Pro
Handles stock tracking, low stock alerts, and inventory reports
"""

from flask import Blueprint, request, jsonify
from db.models import Product, OrderItem, db
from api.auth import token_required, admin_required
from datetime import datetime, timedelta
from sqlalchemy import func

inventory = Blueprint('inventory', __name__)

@inventory.route('/stock/check', methods=['GET'])
@token_required
def check_stock_levels(current_user):
    """Check current stock levels for all products"""
    try:
        products = Product.query.all()
        stock_info = []
        
        for product in products:
            # Calculate current stock (if stock tracking is enabled)
            current_stock = getattr(product, 'stock', None)
            low_stock_threshold = getattr(product, 'low_stock_threshold', 10)
            
            stock_status = 'unknown'
            if current_stock is not None:
                if current_stock <= 0:
                    stock_status = 'out_of_stock'
                elif current_stock <= low_stock_threshold:
                    stock_status = 'low_stock'
                else:
                    stock_status = 'in_stock'
            
            stock_info.append({
                'id': product.id,
                'name': product.name,
                'category': product.category,
                'current_stock': current_stock,
                'low_stock_threshold': low_stock_threshold,
                'status': stock_status,
                'price': float(product.price)
            })
        
        return jsonify(stock_info), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@inventory.route('/stock/low', methods=['GET'])
@token_required
def get_low_stock_items(current_user):
    """Get items with low stock levels"""
    try:
        # This would work if we had stock fields in the Product model
        # For now, we'll simulate low stock detection
        products = Product.query.all()
        low_stock_items = []
        
        for product in products:
            # Simulate stock levels based on product ID for demo
            simulated_stock = (product.id * 7) % 25  # Random-ish stock levels
            low_stock_threshold = 10
            
            if simulated_stock <= low_stock_threshold:
                low_stock_items.append({
                    'id': product.id,
                    'name': product.name,
                    'category': product.category,
                    'current_stock': simulated_stock,
                    'low_stock_threshold': low_stock_threshold,
                    'suggested_reorder': max(50, low_stock_threshold * 3),
                    'price': float(product.price)
                })
        
        return jsonify(low_stock_items), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@inventory.route('/stock/update', methods=['POST'])
@admin_required
def update_stock(current_user):
    """Update stock levels for products"""
    try:
        data = request.get_json()
        
        if not data or 'updates' not in data:
            return jsonify({'error': 'Stock updates are required'}), 400
        
        updated_products = []
        
        for update in data['updates']:
            product_id = update.get('product_id')
            new_stock = update.get('stock')
            
            if not product_id or new_stock is None:
                continue
            
            product = Product.query.get(product_id)
            if product:
                # If the Product model had a stock field, we would update it here
                # For now, we'll just track the update
                updated_products.append({
                    'id': product.id,
                    'name': product.name,
                    'old_stock': getattr(product, 'stock', 'N/A'),
                    'new_stock': new_stock
                })
        
        # In a real implementation, you would commit the changes to the database
        # db.session.commit()
        
        return jsonify({
            'message': f'Updated stock for {len(updated_products)} products',
            'updated_products': updated_products
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@inventory.route('/usage/report', methods=['GET'])
@admin_required
def get_usage_report(current_user):
    """Get inventory usage report"""
    try:
        # Get date range from query parameters
        days = request.args.get('days', 30, type=int)
        start_date = datetime.now() - timedelta(days=days)
        
        # Get product usage from order items
        usage_data = db.session.query(
            Product.id,
            Product.name,
            Product.category,
            func.sum(OrderItem.quantity).label('total_used'),
            func.count(OrderItem.id).label('order_count'),
            func.avg(OrderItem.quantity).label('avg_per_order')
        ).join(OrderItem, Product.id == OrderItem.product_id) \
         .join(Order, OrderItem.order_id == Order.id) \
         .filter(Order.created_at >= start_date) \
         .group_by(Product.id) \
         .order_by(func.sum(OrderItem.quantity).desc()) \
         .all()
        
        usage_report = []
        for item in usage_data:
            # Calculate projected usage
            daily_usage = float(item.total_used) / days
            weekly_projection = daily_usage * 7
            monthly_projection = daily_usage * 30
            
            usage_report.append({
                'product_id': item.id,
                'name': item.name,
                'category': item.category,
                'total_used': int(item.total_used),
                'order_count': int(item.order_count),
                'avg_per_order': round(float(item.avg_per_order), 2),
                'daily_usage': round(daily_usage, 2),
                'weekly_projection': round(weekly_projection, 2),
                'monthly_projection': round(monthly_projection, 2)
            })
        
        return jsonify({
            'period_days': days,
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': datetime.now().strftime('%Y-%m-%d'),
            'usage_data': usage_report
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@inventory.route('/reorder/suggestions', methods=['GET'])
@admin_required
def get_reorder_suggestions(current_user):
    """Get reorder suggestions based on usage patterns"""
    try:
        # Get usage data for the last 30 days
        days = 30
        start_date = datetime.now() - timedelta(days=days)
        
        usage_data = db.session.query(
            Product.id,
            Product.name,
            Product.category,
            Product.price,
            func.sum(OrderItem.quantity).label('total_used')
        ).join(OrderItem, Product.id == OrderItem.product_id) \
         .join(Order, OrderItem.order_id == Order.id) \
         .filter(Order.created_at >= start_date) \
         .group_by(Product.id) \
         .all()
        
        reorder_suggestions = []
        
        for item in usage_data:
            daily_usage = float(item.total_used) / days
            
            # Simulate current stock and reorder point
            simulated_current_stock = (item.id * 7) % 25
            reorder_point = max(10, int(daily_usage * 7))  # 1 week buffer
            optimal_order_quantity = max(50, int(daily_usage * 30))  # 1 month supply
            
            if simulated_current_stock <= reorder_point:
                reorder_suggestions.append({
                    'product_id': item.id,
                    'name': item.name,
                    'category': item.category,
                    'current_stock': simulated_current_stock,
                    'reorder_point': reorder_point,
                    'suggested_quantity': optimal_order_quantity,
                    'daily_usage': round(daily_usage, 2),
                    'unit_price': float(item.price),
                    'total_cost': round(float(item.price) * optimal_order_quantity, 2),
                    'urgency': 'high' if simulated_current_stock <= 5 else 'medium'
                })
        
        # Sort by urgency and current stock level
        reorder_suggestions.sort(key=lambda x: (x['urgency'] == 'high', -x['current_stock']))
        
        return jsonify({
            'suggestions': reorder_suggestions,
            'total_items': len(reorder_suggestions),
            'total_cost': sum(item['total_cost'] for item in reorder_suggestions)
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@inventory.route('/categories/performance', methods=['GET'])
@admin_required
def get_category_performance(current_user):
    """Get performance metrics by category"""
    try:
        # Get date range
        days = request.args.get('days', 30, type=int)
        start_date = datetime.now() - timedelta(days=days)
        
        category_data = db.session.query(
            Product.category,
            func.sum(OrderItem.quantity).label('total_quantity'),
            func.sum(Product.price * OrderItem.quantity).label('total_revenue'),
            func.count(func.distinct(Product.id)).label('unique_products'),
            func.count(OrderItem.id).label('total_orders')
        ).join(OrderItem, Product.id == OrderItem.product_id) \
         .join(Order, OrderItem.order_id == Order.id) \
         .filter(Order.created_at >= start_date) \
         .group_by(Product.category) \
         .order_by(func.sum(Product.price * OrderItem.quantity).desc()) \
         .all()
        
        performance_data = []
        total_revenue = sum(float(item.total_revenue) for item in category_data)
        
        for item in category_data:
            revenue = float(item.total_revenue)
            revenue_percentage = (revenue / total_revenue * 100) if total_revenue > 0 else 0
            
            performance_data.append({
                'category': item.category,
                'total_quantity': int(item.total_quantity),
                'total_revenue': revenue,
                'revenue_percentage': round(revenue_percentage, 2),
                'unique_products': int(item.unique_products),
                'total_orders': int(item.total_orders),
                'avg_order_value': round(revenue / int(item.total_orders), 2) if item.total_orders > 0 else 0
            })
        
        return jsonify({
            'period_days': days,
            'categories': performance_data,
            'total_revenue': total_revenue
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@inventory.route('/alerts', methods=['GET'])
@token_required
def get_inventory_alerts(current_user):
    """Get inventory alerts and notifications"""
    try:
        alerts = []
        
        # Simulate various inventory alerts
        products = Product.query.limit(10).all()
        
        for product in products:
            simulated_stock = (product.id * 7) % 25
            
            # Low stock alert
            if simulated_stock <= 5:
                alerts.append({
                    'type': 'low_stock',
                    'severity': 'high',
                    'product_id': product.id,
                    'product_name': product.name,
                    'message': f'{product.name} is critically low (Stock: {simulated_stock})',
                    'action_required': 'Reorder immediately',
                    'timestamp': datetime.now().isoformat()
                })
            elif simulated_stock <= 10:
                alerts.append({
                    'type': 'low_stock',
                    'severity': 'medium',
                    'product_id': product.id,
                    'product_name': product.name,
                    'message': f'{product.name} is running low (Stock: {simulated_stock})',
                    'action_required': 'Consider reordering',
                    'timestamp': datetime.now().isoformat()
                })
        
        # Sort alerts by severity
        severity_order = {'high': 0, 'medium': 1, 'low': 2}
        alerts.sort(key=lambda x: severity_order.get(x['severity'], 3))
        
        return jsonify({
            'alerts': alerts,
            'total_alerts': len(alerts),
            'high_priority': len([a for a in alerts if a['severity'] == 'high']),
            'medium_priority': len([a for a in alerts if a['severity'] == 'medium'])
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
