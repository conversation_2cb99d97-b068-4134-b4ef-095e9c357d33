"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildPkg = exports.flatAsync = exports.signApp = exports.signAsync = exports.flat = exports.sign = void 0;
const sign_1 = require("./sign");
Object.defineProperty(exports, "sign", { enumerable: true, get: function () { return sign_1.sign; } });
Object.defineProperty(exports, "signAsync", { enumerable: true, get: function () { return sign_1.signApp; } });
Object.defineProperty(exports, "signApp", { enumerable: true, get: function () { return sign_1.signApp; } });
const flat_1 = require("./flat");
Object.defineProperty(exports, "flat", { enumerable: true, get: function () { return flat_1.flat; } });
Object.defineProperty(exports, "flatAsync", { enumerable: true, get: function () { return flat_1.buildPkg; } });
Object.defineProperty(exports, "buildPkg", { enumerable: true, get: function () { return flat_1.buildPkg; } });
// TODO: Remove and leave only proper named exports, but for non-breaking change reasons
// we need to keep this weirdness for now
module.exports = sign_1.sign;
module.exports.sign = sign_1.sign;
module.exports.signAsync = sign_1.signApp;
module.exports.signApp = sign_1.signApp;
module.exports.flat = flat_1.flat;
module.exports.flatAsync = flat_1.buildPkg;
module.exports.buildPkg = flat_1.buildPkg;
__exportStar(require("./types"), exports);
//# sourceMappingURL=index.js.map