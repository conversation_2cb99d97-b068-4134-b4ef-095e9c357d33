@echo off
echo ========================================
echo    Cash Register Pro - Build Script
echo ========================================
echo.

echo [1/4] Installing Backend Dependencies...
cd backend
pip install Flask Flask-SQLAlchemy Flask-CORS PyJWT Werkzeug python-dotenv
if %errorlevel% neq 0 (
    echo ERROR: Failed to install backend dependencies
    pause
    exit /b 1
)
echo Backend dependencies installed successfully!
echo.

echo [2/4] Installing Frontend Dependencies...
cd ..\frontend
npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install frontend dependencies
    pause
    exit /b 1
)
echo Frontend dependencies installed successfully!
echo.

echo [3/4] Building Electron Application...
npm run dist
if %errorlevel% neq 0 (
    echo ERROR: Failed to build Electron application
    pause
    exit /b 1
)
echo Electron application built successfully!
echo.

echo [4/4] Build Complete!
echo.
echo ========================================
echo Build completed successfully!
echo.
echo Executable files are located in:
echo   frontend\dist\
echo.
echo To run the application:
echo   1. Start backend: python backend\app.py
echo   2. Run frontend: frontend\dist\Cash Register Pro.exe
echo ========================================
echo.
pause
