# 🚀 Cash Register Pro - Installation Guide

## 📋 Prerequisites

Before installing Cash Register Pro, ensure your system meets the following requirements:

### System Requirements
- **Operating System**: Windows 10/11, macOS 10.14+, or Linux Ubuntu 18.04+
- **RAM**: Minimum 4GB, Recommended 8GB
- **Storage**: 1GB free space
- **Network**: Internet connection for initial setup

### Required Software
1. **Python 3.8+** - For the backend server
2. **Node.js 16+** - For the frontend application
3. **npm** - Comes with Node.js

## 🔧 Installation Methods

### Method 1: Quick Start (Recommended)

#### Windows Users
1. **Download and extract** the Cash Register Pro package
2. **Double-click** `run.bat` to start the application
3. **Login** with default credentials:
   - Admin: `admin` / `admin123`
   - Cashier: `cashier` / `cashier123`

#### macOS/Linux Users
1. **Open Terminal** in the project directory
2. **Run the startup script**:
   ```bash
   chmod +x run.sh
   ./run.sh
   ```
3. **Login** with default credentials

### Method 2: Manual Installation

#### Step 1: Install Python Dependencies
```bash
cd backend
pip install Flask Flask-SQLAlchemy Flask-CORS PyJWT Werkzeug python-dotenv
```

#### Step 2: Install Node.js Dependencies
```bash
cd frontend
npm install
```

#### Step 3: Start the Backend Server
```bash
cd backend
python app.py
```

#### Step 4: Start the Frontend Application
```bash
cd frontend
npm start
```

### Method 3: Build Executable

#### Windows
```bash
# Run the build script
build.bat

# Or manually:
cd frontend
npm run dist
```

#### macOS/Linux
```bash
cd frontend
npm run dist:mac    # For macOS
npm run dist:linux  # For Linux
```

## 🗂️ Project Structure

After installation, your directory should look like this:

```
cash_register_app/
├── backend/                 # Flask API Backend
│   ├── api/                # API endpoints
│   ├── db/                 # Database models
│   ├── app.py              # Main Flask app
│   └── requirements.txt    # Python dependencies
├── frontend/               # Electron Frontend
│   ├── assets/            # Icons and images
│   ├── node_modules/      # Node.js dependencies
│   ├── dist/              # Built executables
│   ├── *.html             # Application pages
│   ├── *.js               # Application logic
│   ├── *.css              # Styling
│   └── package.json       # Node.js config
├── README.md              # Project documentation
├── USER_MANUAL.md         # User guide
├── run.bat               # Windows startup script
└── build.bat             # Windows build script
```

## 🔐 Initial Configuration

### Default Users
The application comes with pre-configured users:

| Username | Password  | Role     | Permissions |
|----------|-----------|----------|-------------|
| admin    | admin123  | Admin    | Full access |
| cashier  | cashier123| Cashier  | POS operations |

### Database Setup
- **SQLite database** is created automatically on first run
- **Sample data** is populated for testing
- **Database file** location: `backend/cash_register.db`

### Configuration Files
- **Backend config**: `backend/config.py`
- **Frontend config**: `frontend/package.json`
- **Environment variables**: Create `.env` file if needed

## 🌐 Network Configuration

### Default Ports
- **Backend API**: http://localhost:5000
- **Frontend**: Electron desktop application

### Firewall Settings
If using on a network:
1. **Allow port 5000** through firewall
2. **Update API URL** in frontend configuration
3. **Test connectivity** between devices

### CORS Configuration
The backend is configured to allow cross-origin requests from:
- `http://localhost:*`
- `file://*` (for Electron)

## 🔧 Troubleshooting Installation

### Common Issues

#### Python Not Found
**Error**: `'python' is not recognized as an internal or external command`

**Solution**:
1. Install Python from https://python.org
2. Add Python to system PATH
3. Restart command prompt

#### Node.js Not Found
**Error**: `'npm' is not recognized as an internal or external command`

**Solution**:
1. Install Node.js from https://nodejs.org
2. Restart command prompt
3. Verify with `node --version`

#### Permission Denied
**Error**: `Permission denied` or `Access is denied`

**Solution**:
1. Run command prompt as Administrator
2. Check file permissions
3. Disable antivirus temporarily

#### Port Already in Use
**Error**: `Port 5000 is already in use`

**Solution**:
1. Stop other applications using port 5000
2. Change port in `backend/config.py`
3. Update frontend API URL accordingly

#### Module Not Found
**Error**: `ModuleNotFoundError: No module named 'flask'`

**Solution**:
1. Activate virtual environment if using one
2. Install dependencies: `pip install -r requirements.txt`
3. Check Python version compatibility

### Verification Steps

#### Backend Verification
1. **Open browser** to http://localhost:5000
2. **Should see**: `{"message": "Cash Register API Running"}`
3. **Test login**: POST to `/api/auth/login` with credentials

#### Frontend Verification
1. **Electron window** should open
2. **Login screen** should be visible
3. **Theme toggle** should work
4. **Window controls** should be functional

## 🔄 Updates and Maintenance

### Updating the Application
1. **Backup your data** (database and configuration)
2. **Download new version**
3. **Replace application files**
4. **Restart the application**

### Database Backup
```bash
# Backup database
cp backend/cash_register.db backup/cash_register_backup_$(date +%Y%m%d).db

# Restore database
cp backup/cash_register_backup_YYYYMMDD.db backend/cash_register.db
```

### Log Files
- **Backend logs**: Console output or log files
- **Frontend logs**: Electron DevTools console
- **Error logs**: Check system event logs

## 🚀 Production Deployment

### For Production Use
1. **Change default passwords**
2. **Configure HTTPS** for API
3. **Set up proper database** (PostgreSQL/MySQL)
4. **Configure reverse proxy** (nginx/Apache)
5. **Set up monitoring** and logging
6. **Regular backups** and updates

### Security Considerations
- **Change JWT secret key**
- **Use environment variables** for sensitive data
- **Enable HTTPS** for network deployment
- **Regular security updates**
- **User access auditing**

## 📞 Support

### Getting Help
- **Documentation**: README.md and USER_MANUAL.md
- **Issue Tracking**: GitHub issues or support system
- **Community**: Forums or chat channels

### Reporting Issues
When reporting installation problems:
1. **Operating system** and version
2. **Python version**: `python --version`
3. **Node.js version**: `node --version`
4. **Error messages** (full text)
5. **Installation method** used
6. **Steps to reproduce**

---

**Congratulations!** 🎉 You've successfully installed Cash Register Pro!

For usage instructions, see the [User Manual](USER_MANUAL.md).

**Cash Register Pro** - Your complete point-of-sale solution! 💰
