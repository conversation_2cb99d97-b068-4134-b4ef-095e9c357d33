from flask import Blueprint, request, jsonify, current_app
from functools import wraps
from db.models import User, db
from werkzeug.security import generate_password_hash
import jwt
from datetime import datetime, timedelta

auth = Blueprint('auth', __name__)

# Decorator for verifying the JWT
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        
        # Check if token is in headers
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
        
        if not token:
            return jsonify({'error': 'Token is missing!'}), 401
        
        try:
            # Decode the token
            data = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user = User.query.filter_by(id=data['user_id']).first()
            
            if not current_user:
                return jsonify({'error': 'Invalid token!'}), 401
            
        except jwt.ExpiredSignatureError:
            return jsonify({'error': 'Token has expired!'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'error': 'Invalid token!'}), 401
        
        # Pass the current user to the route
        return f(current_user, *args, **kwargs)
    
    return decorated

# Decorator for admin-only routes
def admin_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        
        # Check if token is in headers
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
        
        if not token:
            return jsonify({'error': 'Token is missing!'}), 401
        
        try:
            # Decode the token
            data = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
            current_user = User.query.filter_by(id=data['user_id']).first()
            
            if not current_user:
                return jsonify({'error': 'Invalid token!'}), 401
            
            if current_user.role != 'admin':
                return jsonify({'error': 'Admin privileges required!'}), 403
            
        except jwt.ExpiredSignatureError:
            return jsonify({'error': 'Token has expired!'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'error': 'Invalid token!'}), 401
        
        # Pass the current user to the route
        return f(current_user, *args, **kwargs)
    
    return decorated
# Decorator for routes requiring specific roles
def roles_required(allowed_roles):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            token = None
            if 'Authorization' in request.headers:
                auth_header = request.headers['Authorization']
                if auth_header.startswith('Bearer '):
                    token = auth_header.split(' ')[1]
            
            if not token:
                return jsonify({'error': 'Token is missing!'}), 401
            
            try:
                data = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=['HS256'])
                current_user = User.query.filter_by(id=data['user_id']).first()
                
                if not current_user:
                    return jsonify({'error': 'Invalid token!'}), 401
                
                if current_user.role not in allowed_roles:
                    return jsonify({'error': 'Access forbidden: Insufficient privileges!'}), 403
                    
            except jwt.ExpiredSignatureError:
                return jsonify({'error': 'Token has expired!'}), 401
            except jwt.InvalidTokenError:
                return jsonify({'error': 'Invalid token!'}), 401
            
            return f(current_user, *args, **kwargs)
        return decorated_function
    return decorator

@auth.route('/login', methods=['POST'])
def login():
    """Login and get JWT token"""
    try:
        data = request.get_json()
        
        if not data or not data.get('username') or not data.get('password'):
            return jsonify({'error': 'Username and password are required!'}), 400
        
        user = User.query.filter_by(username=data['username']).first()
        
        if not user or not user.check_password(data['password']):
            return jsonify({'error': 'Invalid username or password!'}), 401
        
        # Generate JWT token
        token = jwt.encode({
            'user_id': user.id,
            'username': user.username,
            'role': user.role,
            'exp': datetime.utcnow() + timedelta(hours=24)
        }, current_app.config['SECRET_KEY'], algorithm='HS256')
        
        return jsonify({
            'token': token,
            'username': user.username,
            'role': user.role
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth.route('/signup', methods=['POST'])
def signup():
    """Public signup endpoint for new users"""
    try:
        data = request.get_json()

        if not data or not data.get('username') or not data.get('password'):
            return jsonify({'error': 'Username and password are required!'}), 400

        # Check if username already exists
        if User.query.filter_by(username=data['username']).first():
            return jsonify({'error': 'Username already exists!'}), 400

        # Validate password strength
        if len(data['password']) < 6:
            return jsonify({'error': 'Password must be at least 6 characters long!'}), 400

        # Default role is cashier, admin can be set only by existing admin
        role = data.get('role', 'cashier')
        if role not in ['admin', 'cashier']:
            role = 'cashier'

        # Check if this is the first user (make them admin)
        user_count = User.query.count()
        if user_count == 0:
            role = 'admin'
        elif role == 'admin':
            # Only allow admin role if requested by existing admin
            role = 'cashier'

        # Create new user
        new_user = User(
            username=data['username'],
            password=data['password'],
            role=role
        )

        db.session.add(new_user)
        db.session.commit()

        # Generate JWT token for immediate login
        token = jwt.encode({
            'user_id': new_user.id,
            'username': new_user.username,
            'role': new_user.role,
            'exp': datetime.utcnow() + timedelta(hours=24)
        }, current_app.config['SECRET_KEY'], algorithm='HS256')

        return jsonify({
            'message': 'User registered successfully!',
            'token': token,
            'username': new_user.username,
            'role': new_user.role
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@auth.route('/register', methods=['POST'])
@admin_required
def register(current_user):
    """Register a new user (admin only)"""
    try:
        data = request.get_json()

        if not data or not data.get('username') or not data.get('password') or not data.get('role'):
            return jsonify({'error': 'Username, password, and role are required!'}), 400

        # Check if username already exists
        if User.query.filter_by(username=data['username']).first():
            return jsonify({'error': 'Username already exists!'}), 400

        # Validate role
        if data['role'] not in ['admin', 'cashier']:
            return jsonify({'error': 'Role must be either "admin" or "cashier"!'}), 400

        # Create new user
        new_user = User(
            username=data['username'],
            password=data['password'],
            role=data['role']
        )

        db.session.add(new_user)
        db.session.commit()

        return jsonify({'message': 'User registered successfully!'}), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@auth.route('/users', methods=['GET'])
@admin_required
def get_users(current_user):
    """Get all users (admin only)"""
    try:
        users = User.query.all()
        result = []
        
        for user in users:
            user_data = {
                'id': user.id,
                'username': user.username,
                'role': user.role
            }
            result.append(user_data)
        
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth.route('/users/<int:user_id>', methods=['GET'])
@admin_required
def get_user(current_user, user_id):
    """Get a specific user (admin only)"""
    try:
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found!'}), 404
        
        user_data = {
            'id': user.id,
            'username': user.username,
            'role': user.role
        }
        
        return jsonify(user_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth.route('/users/<int:user_id>', methods=['PUT'])
@admin_required
def update_user(current_user, user_id):
    """Update a user (admin only)"""
    try:
        data = request.get_json()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found!'}), 404
        
        # Update username if provided
        if data.get('username'):
            # Check if username already exists
            existing_user = User.query.filter_by(username=data['username']).first()
            if existing_user and existing_user.id != user_id:
                return jsonify({'error': 'Username already exists!'}), 400
            
            user.username = data['username']
        
        # Update password if provided
        if data.get('password'):
            user.password_hash = generate_password_hash(data['password'])
        
        # Update role if provided
        if data.get('role'):
            if data['role'] not in ['admin', 'cashier']:
                return jsonify({'error': 'Role must be either "admin" or "cashier"!'}), 400
            
            user.role = data['role']
        
        db.session.commit()
        
        return jsonify({'message': 'User updated successfully!'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@auth.route('/users/<int:user_id>', methods=['DELETE'])
@admin_required
def delete_user(current_user, user_id):
    """Delete a user (admin only)"""
    try:
        # Prevent deleting yourself
        if current_user.id == user_id:
            return jsonify({'error': 'Cannot delete your own account!'}), 400
        
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found!'}), 404
        
        db.session.delete(user)
        db.session.commit()
        
        return jsonify({'message': 'User deleted successfully!'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
